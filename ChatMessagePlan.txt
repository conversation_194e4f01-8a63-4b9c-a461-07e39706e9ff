// 修改：CompactPaginatedTable 的分页控制器 - 同一行居中展示
const CompactPaginatedTable = ({ children, isChating, isCurrent, ...props }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5); // 默认更小的页面大小
  const [tableData, setTableData] = useState({ headers: [], rows: [] });

  useEffect(() => {
    // 只有在isChating && isCurrent时才解析表格数据
    if (!isChating || !isCurrent) {
      return;
    }

    // 解析表格数据
    const parseTableData = () => {
      if (!children || !Array.isArray(children)) return;

      let headers = [];
      let rows = [];

      children.forEach(child => {
        if (React.isValidElement(child)) {
          if (child.type === 'thead') {
            // 提取表头
            const theadChildren = React.Children.toArray(child.props.children);
            theadChildren.forEach(tr => {
              if (React.isValidElement(tr) && tr.type === 'tr') {
                const thChildren = React.Children.toArray(tr.props.children);
                headers = thChildren.map(th => {
                  if (React.isValidElement(th) && th.type === 'th') {
                    return th.props.children;
                  }
                  return '';
                });
              }
            });
          } else if (child.type === 'tbody') {
            // 提取表格行
            const tbodyChildren = React.Children.toArray(child.props.children);
            rows = tbodyChildren.map(tr => {
              if (React.isValidElement(tr) && tr.type === 'tr') {
                const tdChildren = React.Children.toArray(tr.props.children);
                return tdChildren.map(td => {
                  if (React.isValidElement(td) && td.type === 'td') {
                    return td.props.children;
                  }
                  return '';
                });
              }
              return [];
            }).filter(row => row.length > 0);
          }
        }
      });

      setTableData({ headers, rows });
    };

    parseTableData();
  }, [children, isChating, isCurrent]);

  // 如果不在聊天中或不是当前消息，显示原始表格
  if (!isChating || !isCurrent) {
    return (
      <div className="my-2 text-xs">
        <div className="overflow-x-auto rounded border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200 bg-white text-xs" {...props}>
            {children}
          </table>
        </div>
      </div>
    );
  }

  // ... 其余代码保持不变 ...