{"extends": "@zhenguo/bf-builder/src/build/typescript/tsconfig.browser.json", "compilerOptions": {"lib": ["ESNext", "DOM", "DOM.Iterable"], "baseUrl": ".", "allowJs": true, "outDir": "./build", "paths": {"@common": ["client/common"], "@common/*": ["client/common/*"], "@config": ["client/config"], "@config/*": ["client/config/*"], "@consts": ["client/consts"], "@consts/*": ["client/consts/*"], "@core": ["client/core"], "@core/*": ["client/core/*"], "@entry": ["client/entry"], "@entry/*": ["client/entry/*"], "@globals": ["client/globals"], "@globals/*": ["client/globals/*"], "@integration": ["client/integration"], "@integration/*": ["client/integration/*"], "@pages": ["client/pages"], "@pages/*": ["client/pages/*"], "@routes": ["client/routes"], "@routes/*": ["client/routes/*"], "@shares": ["client/shares"], "@shares/*": ["client/shares/*"], "@routes-archive": ["client/routes-archive"], "@routes-archive/*": ["client/routes-archive/*"], "@shares-archive": ["client/shares-archive"], "@shares-archive/*": ["client/shares-archive/*"]}}, "include": ["client"]}