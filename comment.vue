<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getVisitDetail } from './config/api';
import VisitImages from './components/VisitImages.vue';
import VisitFiles from './components/VisitFiles.vue';
import { visitTypeMap, clueStatusMap } from './config/enums';
import { addError, OwlErrorLevel } from '@/utils/owl';

interface VisitPic {
  name: string;
  url: string;
}

interface VisitFile {
  name: string;
  url: string;
}

// 定义API返回的数据结构
interface VisitDetailResponse {
  code: number;
  msg: string;
  data: {
    visitId: number;
    visitorName: string;
    visitorMis: string;
    hostName: string;
    hostPhoneNumber: string;
    visitConclusion: string;
    visitTime: string;
    visitTypeDesc: string;
    visitIntentionDesc: string;
    visitAddress: string;
    clueId: number;
    franchiseeId: number;
    franchiseeName: string;
    investmentStatusDesc: string;
    visitPicList: VisitPic[];
    visitFileList: VisitFile[];
  };
  success: boolean;
}

@Component({
  components: {
    VisitImages,
    VisitFiles
  }
})
export default class VisitDetail extends Vue {
  // 拜访ID
  private visitId = '';

  // 加载状态
  private loading = false;

  // 拜访基本信息
  private visitInfo: any = {
    visitId: '',
    visitorName: '',
    visitorMis: '',
    visitTypeDesc: '',
    visitIntentionDesc: '',
    visitTime: ''
  };

  // 线索信息
  private clueInfo: any = {
    clueId: '',
    franchiseeId: '',
    franchiseeName: '',
    investmentStatusDesc: ''
  };

  // 拜访详情
  private visitDetailInfo: any = {
    hostName: '',
    hostPhoneNumber: '',
    visitAddress: '',
    visitConclusion: '',
    visitPicList: [] as VisitPic[],
    visitFileList: [] as VisitFile[]
  };

  created() {
    // 从URL中获取拜访ID，不使用$route
    const urlParams = new URLSearchParams(window.location.search);
    this.visitId = urlParams.get('visitId') || '';

    if (this.visitId) {
      this.fetchVisitDetail();
    } else {
      (this as any).$mtd.message.error('拜访ID不能为空');
    }
  }

  /**
   * 获取拜访详情
   */
  private async fetchVisitDetail() {
    if (this.loading) return;

    this.loading = true;
    try {
      console.log('开始获取拜访详情，拜访ID:', this.visitId);

      // 使用any类型暂时绕过TypeScript的类型检查
      const apiResponse = await getVisitDetail(this.visitId) as any;
      console.log('拜访详情接口返回:', apiResponse);

      if (apiResponse && apiResponse.data) {
        const apiData = apiResponse.data as any;

        // 基础信息
        this.visitInfo = {
          visitId: apiData.visitId || '',
          visitorName: apiData.visitorName || '',
          visitorMis: apiData.visitorMis || '',
          visitTypeDesc: apiData.visitTypeDesc || '',
          visitIntentionDesc: apiData.visitIntentionDesc || '',
          visitTime: apiData.visitTime || ''
        };

        // 线索信息
        this.clueInfo = {
          clueId: apiData.clueId || '',
          franchiseeId: apiData.franchiseeId || '',
          franchiseeName: apiData.franchiseeName || '',
          investmentStatusDesc: apiData.investmentStatusDesc || ''
        };

        // 拜访详情
        this.visitDetailInfo = {
          hostName: apiData.hostName || '',
          hostPhoneNumber: apiData.hostPhoneNumber || '',
          visitAddress: apiData.visitAddress || '',
          visitConclusion: apiData.visitConclusion || '',
          visitPicList: Array.isArray(apiData.visitPicList) ? apiData.visitPicList : [],
          visitFileList: Array.isArray(apiData.visitFileList) ? apiData.visitFileList : []
        };

        // 调试输出
        console.log('处理后的拜访详情数据:', {
          visitInfo: this.visitInfo,
          clueInfo: this.clueInfo,
          visitDetailInfo: this.visitDetailInfo
        });

        // 特别检查图片和文件列表
        console.log('图片列表:', this.visitDetailInfo.visitPicList);
        console.log('文件列表:', this.visitDetailInfo.visitFileList);
      } else {
        console.error('接口返回数据为空');
        (this as any).$mtd.message.error('获取拜访详情失败：接口返回数据为空');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取拜访详情失败:', error);
      (this as any).$mtd.message.error('获取拜访详情失败');
      addError({
        name: 'VISIT_DETAIL_FETCH_ERROR',
        msg: `获取拜访详情失败: ${error}`
      }, {
        level: OwlErrorLevel.ERROR,
        tags: {
          visitId: this.visitId,
          url: window.location.href
        }
      });
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.visit-detail-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    .merchant-name {
      font-size: 20px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 16px;
    }

    .header-info {
      display: flex;

      .info-item {
        margin-right: 40px;
        color: #606266;
      }
    }
  }

  .page-content {
    .content-section {
      background-color: #fff;
      border-radius: 4px;
      margin-bottom: 20px;

      .section-title {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        .title-bar {
          width: 4px;
          height: 16px;
          background-color: #409EFF;
          margin-right: 8px;
        }

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }
      }

      .section-content {
        padding: 20px;
      }
    }
  }
}
</style>
<template>
  <xx-layout title="拜访详情">
    <div slot="content" class="visit-detail-container">
      <mtd-loading :loading="loading" fullscreen>
        <!-- 页面标题模块 -->
        <div class="page-header">
          <div class="merchant-name">{{ clueInfo.franchiseeName || '拜访详情' }}</div>
          <div class="header-info">
            <div class="info-item">拜访ID: {{ visitInfo.visitId || '--' }}</div>
            <div class="info-item">线索ID: {{ clueInfo.clueId || '--' }}</div>
            <div class="info-item">合作商ID: {{ clueInfo.franchiseeId || '--' }}</div>
          </div>
        </div>

        <!-- 内容模块 -->
        <div class="page-content">
          <!-- 基础信息 -->
          <div class="content-section">
            <div class="section-title">
              <div class="title-bar"></div>
              <h3>基本信息</h3>
            </div>
            <div class="section-content">
              <log-form
                :model="visitInfo"
                :labelWidth="120"
                labelPosition="right"
                :disabled="true"
              >
                <log-form-item label="拜访记录ID">
                  {{ visitInfo.visitId || '--' }}
                </log-form-item>
                <log-form-item label="拜访人">
                  {{ visitInfo.visitorName || '--' }}
                </log-form-item>
                <log-form-item label="拜访人MIS">
                  {{ visitInfo.visitorMis || '--' }}
                </log-form-item>
                <log-form-item label="拜访方式">
                  {{ visitInfo.visitTypeDesc || '--' }}
                </log-form-item>
                <log-form-item label="拜访意向">
                  {{ visitInfo.visitIntentionDesc || '--' }}
                </log-form-item>
                <log-form-item label="拜访时间">
                  {{ visitInfo.visitTime || '--' }}
                </log-form-item>
              </log-form>
            </div>
          </div>

          <!-- 线索信息 -->
          <div class="content-section">
            <div class="section-title">
              <div class="title-bar"></div>
              <h3>线索信息</h3>
            </div>
            <div class="section-content">
              <log-form
                :model="clueInfo"
                :labelWidth="120"
                labelPosition="right"
                :disabled="true"
              >
                <log-form-item label="线索ID">
                  {{ clueInfo.clueId || '--' }}
                </log-form-item>
                <log-form-item label="合作商ID">
                  {{ clueInfo.franchiseeId || '--' }}
                </log-form-item>
                <log-form-item label="合作商名称">
                  {{ clueInfo.franchiseeName || '--' }}
                </log-form-item>
                <log-form-item label="招商状态">
                  {{ clueInfo.investmentStatusDesc || '--' }}
                </log-form-item>
              </log-form>
            </div>
          </div>

          <!-- 拜访详情 -->
          <div class="content-section">
            <div class="section-title">
              <div class="title-bar"></div>
              <h3>拜访详情</h3>
            </div>
            <div class="section-content">
              <log-form
                :model="visitDetailInfo"
                :labelWidth="120"
                labelPosition="right"
                :disabled="true"
              >
                <log-form-item label="拜访对象">
                  {{ visitDetailInfo.hostName || '--' }}
                </log-form-item>
                <log-form-item label="拜访对象联系电话">
                  {{ visitDetailInfo.hostPhoneNumber || '--' }}
                </log-form-item>
                <log-form-item label="拜访地址">
                  {{ visitDetailInfo.visitAddress || '--' }}
                </log-form-item>
                <log-form-item label="拜访结论">
                  {{ visitDetailInfo.visitConclusion || '--' }}
                </log-form-item>
                <log-form-item label="拜访图片">
                  <VisitImages :images="visitDetailInfo.visitPicList" />
                </log-form-item>
                <log-form-item label="拜访文件">
                  <VisitFiles :files="visitDetailInfo.visitFileList" />
                </log-form-item>
              </log-form>
            </div>
          </div>
        </div>
      </mtd-loading>
    </div>
  </xx-layout>
</template>