import React, {useEffect, useRef, useState} from 'react';
import ReactMarkdown from 'react-markdown';
import {TaskStatus} from '@/config/taskStatus';
import remarkGfm from 'remark-gfm';
import {Check, CheckCircle2, ChevronDown, ChevronLeft, ChevronRight, ChevronUp, Circle, Clock, Copy, Loader2, Minus, XCircle} from 'lucide-react';
import {cn} from '@/lib/utils';
import {formatFullDateTime} from '@/utils/dateUtils';
import rehypeHighlight from 'rehype-highlight'; // 新增：导入代码高亮样式
import 'highlight.js/styles/github.css'; // 新增：代码块复制组件

// 新增：代码块复制组件
const CodeBlock = ({ children, className, ...props }) => {
  const [copied, setCopied] = useState(false);
  const codeRef = useRef(null);

  // 提取语言类型
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';

  const copyToClipboard = async () => {
    if (codeRef.current) {
      const code = codeRef.current.textContent;
      try {
        await navigator.clipboard.writeText(code);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('复制失败:', err);
      }
    }
  };

  return (
    <div className="relative group">
      {language && (
        <div className="flex items-center justify-between bg-gray-100 px-4 py-2 text-xs text-gray-600 border-b border-gray-200 rounded-t-lg">
          <span className="font-medium uppercase">{language}</span>
          <button
            onClick={copyToClipboard}
            className="flex items-center gap-1 px-2 py-1 rounded bg-white hover:bg-gray-50 border border-gray-200 transition-colors duration-200 opacity-0 group-hover:opacity-100"
          >
            {copied ? (
              <>
                <Check className="h-3 w-3 text-green-500" />
                <span className="text-green-500">已复制</span>
              </>
            ) : (
              <>
                <Copy className="h-3 w-3" />
                <span>复制</span>
              </>
            )}
          </button>
        </div>
      )}
      <pre className={cn(
        "overflow-x-auto transition-all duration-300 hover:shadow-md",
        language ? "rounded-b-lg rounded-t-none" : "rounded-lg",
        "bg-gray-50 border border-gray-200"
      )}>
        <code
          ref={codeRef}
          className={cn(
            className,
            "block p-4 text-sm leading-relaxed font-mono",
            "whitespace-pre transition-all duration-300"
          )}
          {...props}
        >
          {children}
        </code>
      </pre>
    </div>
  );
};

// 新增：内联代码组件
const InlineCode = ({ children, ...props }) => (
  <code
    className="px-1.5 py-0.5 bg-gray-100 text-gray-800 rounded text-sm font-mono border border-gray-200 transition-colors duration-200 hover:bg-gray-200"
    {...props}
  >
    {children}
  </code>
);

const TimelineItem = ({ step, isChating, isCurrent }) => {
  const [showFullResult, setShowFullResult] = useState(false);
  const resultRef = useRef(null);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [displayedText, setDisplayedText] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);
  const timelineRef = useRef(null);
  const hasAnimatedRef = useRef(false); // 新增：标记是否已经执行过动画

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    if (resultRef.current) {
      // 修改：检查内容是否超过一行
      const lineHeight = parseFloat(getComputedStyle(resultRef.current).lineHeight);
      const hasOverflow = resultRef.current.scrollHeight > lineHeight * 1.2; // 1.2倍行高作为阈值
      setIsOverflowing(hasOverflow);
    }

    return () => clearTimeout(timer);
  }, [step.result, displayedText]); // 添加 displayedText 依赖

  useEffect(() => {
    // 修改：只有在满足条件且未执行过动画时才执行打字机效果
    if (step.result && !isAnimating && isChating && isCurrent && !hasAnimatedRef.current) {
      hasAnimatedRef.current = true; // 标记已执行动画
      setIsAnimating(true);
      let index = 0;
      const text = step.result;
      const animationInterval = setInterval(() => {
        if (index <= text.length) {
          setDisplayedText(text.slice(0, index));
          index += 8;
        } else {
          clearInterval(animationInterval);
          setIsAnimating(false);
        }
      }, 10);

      return () => clearInterval(animationInterval);
    } else if (step.result && (!isChating || hasAnimatedRef.current)) {
      // 修改：如果不在聊天中或已执行过动画，直接显示完整文本
      setDisplayedText(step.result);
    }
  }, [step.result, isChating, isCurrent]);

  // 新增：重置动画标记的条件
  useEffect(() => {
    if (!isChating) {
      hasAnimatedRef.current = false;
    }
  }, [isChating]);

  const getStatusIcon = () => {
    if (step.result && step.status === TaskStatus.COMPLETED) {
      return <CheckCircle2 className="h-4 w-4 text-green-500" />;
    }
    switch (step.status) {
      case TaskStatus.COMPLETED:
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case TaskStatus.RUNNING:
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case TaskStatus.PLANNING:
        return (
          <div className="relative h-4 w-4">
            <Circle className="h-4 w-4 text-blue-500" />
            <Minus className="h-2 w-2 text-blue-500 absolute top-1 left-1" />
          </div>
        );
      case TaskStatus.FAILED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      case TaskStatus.PENDING:
      default:
        return <Circle className="h-4 w-4 text-gray-300" />;
    }
  };

  const getTimelineColor = () => {
    if (step.status === TaskStatus.FAILED) {
      return "before:bg-gradient-to-b before:from-red-500 before:to-transparent";
    } else if (step.result || step.status === TaskStatus.COMPLETED) {
      return "before:bg-gradient-to-b before:from-green-500 before:to-transparent";
    } else if (step.status === TaskStatus.RUNNING) {
      return "before:bg-gradient-to-b before:from-blue-500 before:to-transparent";
    } else if (step.status === TaskStatus.PLANNING) {
      return "before:bg-gradient-to-b before:from-sky-500 before:to-transparent";
    }
    return "before:bg-gradient-to-b before:from-gray-300 before:to-transparent";
  };

  const getStatusBackground = () => {
    if (step.status === TaskStatus.FAILED) {
      return "bg-gradient-to-br from-red-100 to-red-50 shadow-lg shadow-red-100/50";
    } else if (step.result || step.status === TaskStatus.COMPLETED) {
      return "bg-gradient-to-br from-green-100 to-green-50 shadow-lg shadow-green-100/50";
    } else if (step.status === TaskStatus.RUNNING) {
      return "bg-gradient-to-br from-blue-100 to-blue-50 shadow-lg shadow-blue-100/50 animate-pulse";
    } else if (step.status === TaskStatus.PLANNING) {
      return "bg-gradient-to-br from-sky-100 to-sky-50 shadow-lg shadow-sky-100/50";
    }
    return "bg-gradient-to-br from-gray-100 to-gray-50";
  };

  const getTextColor = () => {
    if (step.status === TaskStatus.FAILED) {
      return "text-red-600 font-medium";
    } else if (step.result || step.status === TaskStatus.COMPLETED) {
      return "text-green-600 font-medium";
    } else if (step.status === TaskStatus.RUNNING) {
      return "text-blue-600 font-medium";
    } else if (step.status === TaskStatus.PLANNING) {
      return "text-sky-600 font-medium";
    }
    return "text-gray-400";
  };

  // 新增：为 step.result 定制的 markdown 组件
  const timelineMarkdownComponents = {
    // 使用 CompactPaginatedTable 但样式更小巧
    table: ({ node, ...props }) => (
      <div className="my-2"> {/* 减小外边距 */}
        <CompactPaginatedTable isChating={isChating} isCurrent={isCurrent} {...props} />
      </div>
    )
  };

  return (
    <li
      ref={timelineRef}
      className={cn(
        "flex flex-col gap-2 text-sm relative transition-all duration-500 ease-in-out",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4",
        "before:content-[''] before:absolute before:left-2 before:top-8 before:w-[1px] before:h-[calc(100%-24px)]",
        getTimelineColor()
      )}
      style={{
        height: 'auto',
        willChange: 'transform, opacity',
        transformOrigin: 'top'
      }}
    >
      <div className="flex items-center gap-2">
        <div className={cn(
          "p-1 rounded-full transition-all duration-300 transform",
          getStatusBackground()
        )}>
          {getStatusIcon()}
        </div>
        <span className={cn(
          "flex-1 break-words whitespace-pre-wrap transition-colors duration-300",
          getTextColor()
        )}>
          {step.text}
        </span>
        {step.startTime && (
          <span className="text-xs text-gray-500 whitespace-nowrap ml-4">
            {formatFullDateTime(step.startTime)}
          </span>
        )}
        {step.duration && (
          <span className="text-xs text-gray-500 ml-2 whitespace-nowrap">
            ({(step.duration / 1000).toFixed(2)}秒)
          </span>
        )}
      </div>
      {step.result && (
        <div
          className={cn(
            "ml-6 mt-1 transition-all duration-500 ease-in-out",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
          )}
          style={{
            willChange: 'transform, opacity',
            transformOrigin: 'top'
          }}
        >
          <div className={cn(
            "bg-gradient-to-br from-white to-gray-50/80 rounded-lg p-4 text-xs transition-all duration-300 hover:shadow-lg border border-gray-100/50 backdrop-blur-sm",
            step.status === TaskStatus.FAILED && "border-red-200 bg-red-50/10"
          )}>
            <div className="flex items-center justify-between mb-2">
              <span className={cn(
                "font-medium",
                step.status === TaskStatus.FAILED ? "text-red-700" : "text-gray-700"
              )}>执行结果:</span>
              {isOverflowing && (
                <button
                  onClick={() => setShowFullResult(!showFullResult)}
                  className="text-blue-500 hover:text-blue-600 flex items-center gap-1 transition-colors duration-200"
                >
                  {showFullResult ? (
                    <>
                      <span>收起</span>
                      <ChevronUp className="h-4 w-4" />
                    </>
                  ) : (
                    <>
                      <span>展开</span>
                      <ChevronDown className="h-4 w-4" />
                    </>
                  )}
                </button>
              )}
            </div>
            <div
              ref={resultRef}
              className={cn(
                "whitespace-pre-wrap break-words transition-all duration-500 overflow-x-auto overflow-y-hidden",
                // 修改：只有在内容超过一行且未展开时才限制高度
                !showFullResult && isOverflowing && "max-h-[1.2em] line-clamp-1",
                step.status === TaskStatus.FAILED ? "text-red-700" : "text-gray-700"
              )}
              style={{
                transition: 'max-height 0.5s ease-in-out',
                maxWidth: '100%',
                wordBreak: 'break-all'
              }}
            >
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeHighlight]}
                components={timelineMarkdownComponents}
              >
                {isAnimating ? displayedText : step.result}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      )}
    </li>
  );
};

// 修改：PaginatedTable 的分页控制器 - 同一行居中展示
const PaginatedTable = ({ children, ...props }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [tableData, setTableData] = useState({ headers: [], rows: [] });

  useEffect(() => {
    // 解析表格数据
    const parseTableData = () => {
      if (!children || !Array.isArray(children)) return;

      let headers = [];
      let rows = [];

      children.forEach(child => {
        if (React.isValidElement(child)) {
          if (child.type.name === 'thead') {
            // 提取表头
            const theadChildren = React.Children.toArray(child.props.children);
            theadChildren.forEach(tr => {
              if (React.isValidElement(tr) && tr.type.name === 'tr') {
                const thChildren = React.Children.toArray(tr.props.children);
                headers = thChildren.map(th => {
                  if (React.isValidElement(th) && th.type.name === 'th') {
                    return th.props.children;
                  }
                  return '';
                });
              }
            });
          } else if (child.type.name === 'tbody') {
            // 提取表格行
            const tbodyChildren = React.Children.toArray(child.props.children);
            rows = tbodyChildren.map(tr => {
              if (React.isValidElement(tr) && tr.type.name === 'tr') {
                const tdChildren = React.Children.toArray(tr.props.children);
                return tdChildren.map(td => {
                  if (React.isValidElement(td) && td.type.name === 'td') {
                    return td.props.children;
                  }
                  return '';
                });
              }
              return [];
            }).filter(row => row.length > 0);
          }
        }
      });

      setTableData({ headers, rows });
    };

    parseTableData();
  }, [children]);

  // 计算分页数据
  const totalRows = tableData.rows.length;
  const totalPages = Math.ceil(totalRows / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentRows = tableData.rows.slice(startIndex, endIndex);

  // 重置页码当页面大小改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [pageSize]);

  const handlePageChange = (page) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const handlePageSizeChange = (newSize) => {
    setPageSize(newSize);
  };

  // 如果没有数据或数据少于等于5条，显示原始表格但保持样式一致
  if (totalRows <= 5) {
    return (
      <div className="my-4">
        <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200 bg-white">
            {/* 表头 */}
            <thead className="bg-gray-50">
              <tr className="hover:bg-gray-50 transition-colors duration-150">
                {tableData.headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            {/* 表格内容 */}
            <tbody className="bg-white divide-y divide-gray-200">
              {tableData.rows.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50 transition-colors duration-150">
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                    >
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="my-4">
      {/* 表格容器 */}
      <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200 bg-white">
          {/* 表头 */}
          <thead className="bg-gray-50">
            <tr className="hover:bg-gray-50 transition-colors duration-150">
              {tableData.headers.map((header, index) => (
                <th
                  key={index}
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          {/* 表格内容 */}
          <tbody className="bg-white divide-y divide-gray-200">
            {currentRows.map((row, rowIndex) => (
              <tr key={startIndex + rowIndex} className="hover:bg-gray-50 transition-colors duration-150">
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页控制器 - 修改为同一行居中展示 */}
      <div className="flex items-center justify-end gap-6 mt-4 px-2 flex-wrap">
        {/* 左侧：数据总条数 */}
        <div className="flex items-start gap-2 text-sm text-gray-700">
          <span>共 {totalRows} 条数据</span>
        </div>
        {/* 页面大小选择 */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-700">每页显示:</span>
          <select
            value={pageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value={5}>5条</option>
            <option value={10}>10条</option>
            <option value={20}>20条</option>
            <option value={50}>50条</option>
          </select>
        </div>

        {/* 分页按钮 */}
        <div className="flex items-center gap-2">
          {/* 上一页按钮 */}
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={cn(
              "flex items-center gap-1 px-3 py-1 text-sm rounded-md transition-colors duration-200",
              currentPage === 1
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            )}
          >
            <ChevronLeft className="h-4 w-4" />
            上一页
          </button>

          {/* 页码按钮 */}
          <div className="flex items-center gap-1">
            {/* 第一页 */}
            {totalPages > 0 && (
              <button
                onClick={() => handlePageChange(1)}
                className={cn(
                  "px-3 py-1 text-sm rounded-md transition-colors duration-200",
                  currentPage === 1
                    ? "bg-blue-500 text-white"
                    : "text-gray-700 hover:bg-gray-100"
                )}
              >
                1
              </button>
            )}

            {/* 省略号（前） */}
            {currentPage > 3 && totalPages > 5 && (
              <span className="px-2 text-gray-400">...</span>
            )}

            {/* 中间页码 */}
            {Array.from({ length: totalPages }, (_, i) => i + 1)
              .filter(page => {
                if (totalPages <= 5) return page > 1 && page < totalPages;
                if (currentPage <= 3) return page > 1 && page <= 4 && page < totalPages;
                if (currentPage >= totalPages - 2) return page >= totalPages - 3 && page > 1 && page < totalPages;
                return page >= currentPage - 1 && page <= currentPage + 1 && page !== 1 && page !== totalPages;
              })
              .map(page => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={cn(
                    "px-3 py-1 text-sm rounded-md transition-colors duration-200",
                    currentPage === page
                      ? "bg-blue-500 text-white"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  {page}
                </button>
              ))}

            {/* 省略号（后） */}
            {currentPage < totalPages - 2 && totalPages > 5 && (
              <span className="px-2 text-gray-400">...</span>
            )}

            {/* 最后一页 */}
            {totalPages > 1 && (
              <button
                onClick={() => handlePageChange(totalPages)}
                className={cn(
                  "px-3 py-1 text-sm rounded-md transition-colors duration-200",
                  currentPage === totalPages
                    ? "bg-blue-500 text-white"
                    : "text-gray-700 hover:bg-gray-100"
                )}
              >
                {totalPages}
              </button>
            )}
          </div>

          {/* 下一页按钮 */}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={cn(
              "flex items-center gap-1 px-3 py-1 text-sm rounded-md transition-colors duration-200",
              currentPage === totalPages
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            )}
          >
            下一页
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// 修改：CompactPaginatedTable 的分页控制器 - 同一行居中展示
const CompactPaginatedTable = ({ children, ...props }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5); // 默认更小的页面大小
  const [tableData, setTableData] = useState({ headers: [], rows: [] });

  useEffect(() => {
    // 解析表格数据
    const parseTableData = () => {
      if (!children || !Array.isArray(children)) return;

      let headers = [];
      let rows = [];

      children.forEach(child => {
        if (React.isValidElement(child)) {
          if (child.type === 'thead') {
            // 提取表头
            const theadChildren = React.Children.toArray(child.props.children);
            theadChildren.forEach(tr => {
              if (React.isValidElement(tr) && tr.type === 'tr') {
                const thChildren = React.Children.toArray(tr.props.children);
                headers = thChildren.map(th => {
                  if (React.isValidElement(th) && th.type === 'th') {
                    return th.props.children;
                  }
                  return '';
                });
              }
            });
          } else if (child.type === 'tbody') {
            // 提取表格行
            const tbodyChildren = React.Children.toArray(child.props.children);
            rows = tbodyChildren.map(tr => {
              if (React.isValidElement(tr) && tr.type === 'tr') {
                const tdChildren = React.Children.toArray(tr.props.children);
                return tdChildren.map(td => {
                  if (React.isValidElement(td) && td.type === 'td') {
                    return td.props.children;
                  }
                  return '';
                });
              }
              return [];
            }).filter(row => row.length > 0);
          }
        }
      });

      setTableData({ headers, rows });
    };

    parseTableData();
  }, [children]);

  // 计算分页数据
  const totalRows = tableData.rows.length;
  const totalPages = Math.ceil(totalRows / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentRows = tableData.rows.slice(startIndex, endIndex);

  // 重置页码当页面大小改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [pageSize]);

  const handlePageChange = (page) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  const handlePageSizeChange = (newSize) => {
    setPageSize(newSize);
  };

  // 如果没有数据或数据少于等于5条，显示原始表格但保持样式一致
  if (totalRows <= 5) {
    return (
      <div className="my-2 text-xs">
        <div className="overflow-x-auto rounded border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200 bg-white text-xs">
            {/* 表头 */}
            <thead className="bg-gray-50">
              <tr className="hover:bg-gray-50 transition-colors duration-150">
                {tableData.headers.map((header, index) => (
                  <th
                    key={index}
                    className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            {/* 表格内容 */}
            <tbody className="bg-white divide-y divide-gray-200">
              {tableData.rows.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50 transition-colors duration-150">
                  {row.map((cell, cellIndex) => (
                    <td
                      key={cellIndex}
                      className="px-2 py-1.5 text-xs text-gray-900 border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                    >
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="my-2 text-xs">
      {/* 表格容器 - 保持与PaginatedTable一致的样式 */}
      <div className="overflow-x-auto rounded border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200 bg-white text-xs">
          {/* 表头 */}
          <thead className="bg-gray-50">
            <tr className="hover:bg-gray-50 transition-colors duration-150">
              {tableData.headers.map((header, index) => (
                <th
                  key={index}
                  className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          {/* 表格内容 */}
          <tbody className="bg-white divide-y divide-gray-200">
            {currentRows.map((row, rowIndex) => (
              <tr key={startIndex + rowIndex} className="hover:bg-gray-50 transition-colors duration-150">
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-2 py-1.5 text-xs text-gray-900 border-r border-gray-200 last:border-r-0 whitespace-nowrap"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 分页控制器 - 紧凑版本但保持一致的设计风格 */}
      <div className="flex items-center justify-end gap-4 mt-3 px-1 flex-wrap">
        {/* 左侧：数据总条数 */}
        <div className="flex items-start gap-2 text-sm text-gray-700">
          <span>共 {totalRows} 条数据</span>
        </div>
        {/* 页面大小选择 */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-700">每页显示:</span>
          <select
            value={pageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            className="px-2 py-1 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value={5}>5条</option>
            <option value={10}>10条</option>
            <option value={20}>20条</option>
            <option value={50}>50条</option>
          </select>
        </div>

        {/* 分页按钮 */}
        <div className="flex items-center gap-1">
          {/* 上一页按钮 */}
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={cn(
              "flex items-center gap-1 px-2 py-1 text-xs rounded-md transition-colors duration-200",
              currentPage === 1
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            )}
          >
            <ChevronLeft className="h-3 w-3" />
            <span className="hidden sm:inline">上一页</span>
          </button>

          {/* 页码按钮 - 简化版本但保持核心功能 */}
          <div className="flex items-center gap-1">
            {/* 当前页码显示 */}
            <div className="flex items-center gap-1">
              {totalPages <= 5 ? (
                // 如果总页数少，显示所有页码
                Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={cn(
                      "px-2 py-1 text-xs rounded-md transition-colors duration-200",
                      currentPage === page
                        ? "bg-blue-500 text-white"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    {page}
                  </button>
                ))
              ) : (
                // 如果页数多，显示简化版本
                <>
                  <button
                    onClick={() => handlePageChange(1)}
                    className={cn(
                      "px-2 py-1 text-xs rounded-md transition-colors duration-200",
                      currentPage === 1
                        ? "bg-blue-500 text-white"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    1
                  </button>

                  {currentPage > 3 && <span className="px-1 text-gray-400 text-xs">...</span>}

                  {currentPage > 2 && currentPage < totalPages - 1 && (
                    <button
                      onClick={() => handlePageChange(currentPage)}
                      className="px-2 py-1 text-xs rounded-md bg-blue-500 text-white"
                    >
                      {currentPage}
                    </button>
                  )}

                  {currentPage < totalPages - 2 && <span className="px-1 text-gray-400 text-xs">...</span>}

                  {totalPages > 1 && (
                    <button
                      onClick={() => handlePageChange(totalPages)}
                      className={cn(
                        "px-2 py-1 text-xs rounded-md transition-colors duration-200",
                        currentPage === totalPages
                          ? "bg-blue-500 text-white"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      {totalPages}
                    </button>
                  )}
                </>
              )}
            </div>
          </div>

          {/* 下一页按钮 */}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={cn(
              "flex items-center gap-1 px-2 py-1 text-xs rounded-md transition-colors duration-200",
              currentPage === totalPages
                ? "text-gray-400 cursor-not-allowed"
                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
            )}
          >
            <span className="hidden sm:inline">下一页</span>
            <ChevronRight className="h-3 w-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

const ChatMessage = ({ message, isChating, isCurrent }) => {
  const [showTimeline, setShowTimeline] = useState(true);
  const [showPreText, setShowPreText] = useState(false);
  const contentRef = useRef(null);
  const { role, content, timestamp, timeline = [], preText, isPreTextDone } = message; // 新增：解构isPreTextDone
  const isUser = role === "user";
  const hasTimeline = timeline && timeline.length > 0;
  const hasRenderedRef = useRef(false); // 新增：标记是否已经渲染过

  // 新增：标记组件已渲染
  useEffect(() => {
    hasRenderedRef.current = true;
  }, []);

  // 修改：控制思考过程的自动展开和折叠
  // 新增：监听isPreTextDone变化，自动折叠思考过程
  useEffect(() => {
    if (!isUser && preText && isCurrent) {
      if (isChating && ! isPreTextDone) {
        setShowPreText(true);
      } else {
        setShowPreText(false);
      }
    }
  }, [isPreTextDone, preText, isCurrent, isUser]);

  const renderContent = (content) => {
    if (typeof content === 'string') {
      return content;
    }
    if (Array.isArray(content)) {
      return content.map((item, index) => {
        if (typeof item === 'string') {
          return <span key={index}>{item}</span>;
        }
        if (item && typeof item === 'object') {
          return <span key={index} className={item.className || ''}>{item.text}</span>;
        }
        return null;
      });
    }
    return '';
  };

  const markdownComponents = {
    // 修改：正确处理代码块
    pre: ({ children, ...props }) => {
      // 检查children是否是code元素
      if (React.isValidElement(children) && children.type === 'code') {
        return <CodeBlock {...children.props}>{children.props.children}</CodeBlock>;
      }
      return (
        <pre {...props} className="overflow-x-auto transition-all duration-300 hover:shadow-md rounded-lg bg-gray-50 border border-gray-200 p-4 font-mono whitespace-pre">
          {children}
        </pre>
      );
    },
    // 修改：正确区分内联代码和代码块
    code: ({ node, inline, className, children, ...props }) => {
      // 如果是内联代码（没有被pre包裹）
      if (inline) {
        return <InlineCode {...props}>{children}</InlineCode>;
      }
      // 如果是代码块但没有被pre包裹（直接的code块）
      const match = /language-(\w+)/.exec(className || '');
      if (match) {
        return <CodeBlock className={className} {...props}>{children}</CodeBlock>;
      }
      // 如果是代码块中的code元素，保持原样但添加样式
      return (
        <code
          className={cn(
            className,
            "font-mono text-sm"
          )}
          {...props}
        >
          {children}
        </code>
      );
    },
    // 新增：支持更多编程语言的语法高亮
    div: ({ node, className, ...props }) => {
      if (className && className.includes('highlight')) {
        return (
          <div
            className={cn(
              className,
              "rounded-lg border border-gray-200 overflow-hidden my-4 shadow-sm"
            )}
            {...props}
          />
        );
      }
      return <div className={className} {...props} />;
    },
    // 新增：支持数学公式（如果需要）
    span: ({ node, className, ...props }) => {
      if (className && className.includes('math')) {
        return (
          <span
            className={cn(
              className,
              "inline-block px-2 py-1 bg-blue-50 border border-blue-200 rounded text-blue-800 font-mono text-sm"
            )}
            {...props}
          />
        );
      }
      return <span className={className} {...props} />;
    },
    a: ({ node, ...props }) => (
      <a
        {...props}
        className="text-blue-500 hover:text-blue-600 underline decoration-blue-500/30 hover:decoration-blue-600 transition-colors duration-200"
        target="_blank"
        rel="noopener noreferrer"
      />
    ),
    // 新增：引用块样式
    blockquote: ({ node, ...props }) => (
      <blockquote
        {...props}
        className="border-l-4 border-blue-200 pl-4 py-2 my-4 bg-blue-50/50 text-gray-700 italic rounded-r-lg"
      />
    ),
    // 新增：列表样式
    ul: ({ node, ...props }) => (
      <ul {...props} className="list-disc list-inside space-y-1 my-2 pl-4" />
    ),
    ol: ({ node, ...props }) => (
      <ol {...props} className="list-decimal list-inside space-y-1 my-2 pl-4" />
    ),
    li: ({ node, ...props }) => (
      <li {...props} className="text-gray-700 leading-relaxed" />
    ),
    // 新增：标题样式
    h1: ({ node, ...props }) => (
      <h1 {...props} className="text-2xl font-bold text-gray-800 mt-6 mb-4 border-b border-gray-200 pb-2" />
    ),
    h2: ({ node, ...props }) => (
      <h2 {...props} className="text-xl font-semibold text-gray-800 mt-5 mb-3" />
    ),
    h3: ({ node, ...props }) => (
      <h3 {...props} className="text-lg font-medium text-gray-800 mt-4 mb-2" />
    ),
    h4: ({ node, ...props }) => (
      <h4 {...props} className="text-base font-medium text-gray-800 mt-3 mb-2" />
    ),
    h5: ({ node, ...props }) => (
      <h5 {...props} className="text-sm font-medium text-gray-800 mt-2 mb-1" />
    ),
    h6: ({ node, ...props }) => (
      <h6 {...props} className="text-xs font-medium text-gray-800 mt-2 mb-1" />
    ),
    // 新增：段落样式
    p: ({ node, ...props }) => (
      <p {...props} className="text-gray-700 leading-relaxed my-2" />
    ),
    // 新增：分割线样式
    hr: ({ node, ...props }) => (
      <hr {...props} className="my-6 border-t border-gray-200" />
    ),
    // 修改：使用分页表格组件
    table: ({ node, ...props }) => (
      <PaginatedTable isChating={isChating} isCurrent={isCurrent} {...props} />
    ),
    // 保持原有的表格子组件样式
    thead: ({ node, ...props }) => (
      <thead {...props} className="bg-gray-50" />
    ),
    tbody: ({ node, ...props }) => (
      <tbody {...props} className="bg-white divide-y divide-gray-200" />
    ),
    tr: ({ node, ...props }) => (
      <tr {...props} className="hover:bg-gray-50 transition-colors duration-150" />
    ),
    th: ({ node, ...props }) => (
      <th
        {...props}
        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0 whitespace-nowrap"
      />
    ),
    td: ({ node, ...props }) => (
      <td
        {...props}
        className="px-4 py-3 text-sm text-gray-900 border-r border-gray-200 last:border-r-0 whitespace-nowrap"
      />
    )
  };

  return (
    <div className={cn(
      "flex gap-4 px-6 py-4 mx-auto break-words w-full",
      // 修改：移除transform动画，避免重复渲染时的跳动
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      <div className={cn(
        "flex gap-4 rounded-xl p-4 transition-all duration-300 relative", // 添加 relative 定位
        isUser
          ? "bg-blue-50 hover:bg-blue-100 hover:shadow-xl hover:shadow-blue-100/50 ml-auto"
          : "bg-white hover:shadow-2xl hover:shadow-blue-100/50 border border-gray-100 hover:border-blue-200 w-[80%]",
        isUser ? "max-w-[50%]" : "max-w-[80%]",
        "shadow-lg",
        !isUser && "pb-8 pr-8"
      )}>
        {isChating && isCurrent && !isUser && (
          <div className="absolute bottom-2 right-2 flex items-center gap-1 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1">
            <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
          </div>
        )}

        <div className="flex-shrink-0 w-10 h-10 rounded-full overflow-hidden transform transition-transform duration-300 hover:scale-110 shadow-md">
          {isUser ? (
            <img
              src="https://s3plus.sankuai.com/mcopilot-pub/nocode_image/default/d4463bdbb391a5cc33ea8b36bb1e7e55-mhk3qrdtjih9u6xtcobpgfhwc1dgc0.jpeg"
              alt="小美"
              className="w-full h-full object-cover"
            />
          ) : (
            <img
              src="https://s3plus.sankuai.com/mcopilot-pub/nocode_image/default/ChatGPT%20Image%20Jun%2012%2C%202025%2C%2009_20_23%20PM-771oujzqnqpxebdsl7xj2jmnhmv9kf.png"
              alt="策略助手"
              className="w-full h-full object-cover"
            />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-3 mb-2">
            <span className="font-medium text-sm whitespace-nowrap">
              {isUser ? "小美" : "策略助手"}
            </span>
            <span className="text-xs text-gray-400 whitespace-nowrap">
              {formatFullDateTime(timestamp)}
            </span>
            {hasTimeline && !isUser && (
              <button
                onClick={() => setShowTimeline(!showTimeline)}
                className={cn(
                  "text-xs text-blue-500 hover:text-blue-600 flex items-center gap-1 whitespace-nowrap transition-all duration-300",
                  "transform hover:scale-105 active:scale-95"
                )}
              >
                <Clock className={cn(
                  "h-3 w-3 transition-transform duration-300",
                  showTimeline ? "rotate-0" : "-rotate-180"
                )} />
                <span className="transition-colors duration-300">
                  {showTimeline ? "隐藏时间线" : "显示时间线"}
                </span>
              </button>
            )}
            {isChating && isCurrent && !isUser && (
              <div className="relative flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                <span className="text-xs text-blue-500">执行中...</span>
              </div>
            )}
          </div>

          {/* 修改：preText 思考文本样式 */}
          {!isUser && preText && (
            <div className="mb-4">
              <div className="bg-gradient-to-r from-gray-50 to-gray-100/80 border border-gray-200/60 rounded-lg overflow-hidden shadow-sm">
                {/* 思考文本头部 - 合并为一个按钮 */}
                <button
                  onClick={() => setShowPreText(!showPreText)}
                  className="w-full flex items-center justify-between px-4 py-2 bg-gray-100/50 border-b border-gray-200/40 hover:bg-gray-200/50 transition-colors duration-200"
                >
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                    </div>
                    <span className="text-xs font-medium text-gray-600">思考过程</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    {showPreText ? (
                      <>
                        <span>收起</span>
                        <ChevronUp className="h-3 w-3" />
                      </>
                    ) : (
                      <>
                        <span>展开</span>
                        <ChevronDown className="h-3 w-3" />
                      </>
                    )}
                  </div>
                </button>

                {/* 思考文本内容 */}
                <div className={cn(
                  "transition-all duration-500 ease-in-out overflow-hidden",
                  showPreText ? "max-h-[1000px] opacity-100" : "max-h-0 opacity-0"
                )}>
                  <div className="px-4 py-3 text-sm text-gray-700 leading-relaxed">
                    <div className="prose prose-sm max-w-none prose-gray">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeHighlight]}
                        components={{
                          ...markdownComponents,
                          // 为思考文本定制样式
                          p: ({ node, ...props }) => (
                            <p {...props} className="text-gray-600 leading-relaxed my-2 text-sm" />
                          ),
                          code: ({ node, inline, className, children, ...props }) => {
                            if (inline) {
                              return (
                                <code
                                  className="px-1.5 py-0.5 bg-gray-200/60 text-gray-700 rounded text-xs font-mono border border-gray-300/40"
                                  {...props}
                                >
                                  {children}
                                </code>
                              );
                            }
                            return <InlineCode {...props}>{children}</InlineCode>;
                          },
                          ul: ({ node, ...props }) => (
                            <ul {...props} className="list-disc list-inside space-y-1 my-2 pl-2 text-gray-600" />
                          ),
                          ol: ({ node, ...props }) => (
                            <ol {...props} className="list-decimal list-inside space-y-1 my-2 pl-2 text-gray-600" />
                          ),
                          li: ({ node, ...props }) => (
                            <li {...props} className="text-gray-600 leading-relaxed text-sm" />
                          ),
                          blockquote: ({ node, ...props }) => (
                            <blockquote
                              {...props}
                              className="border-l-3 border-gray-300 pl-3 py-1 my-2 bg-gray-100/30 text-gray-600 italic rounded-r text-sm"
                            />
                          )
                        }}
                      >
                        {isCurrent ? renderContent(preText) : preText}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className={cn(
            "transition-all duration-500 ease-in-out",
            showTimeline ? "opacity-100" : "opacity-0 max-h-0 overflow-hidden"
          )}
          style={{
            transformOrigin: 'top',
            willChange: 'transform, opacity, max-height'
          }}>
            {hasTimeline && !isUser && (
              <div className="mb-3 bg-blue-50/80 p-3 rounded-lg border border-blue-100 transition-all duration-300 hover:shadow-md">
                <ul className="space-y-3">
                  {timeline.map((step, index) => (
                    <TimelineItem
                      key={`${message.id || message.timestamp}-${index}`} // 修改：使用更稳定的key
                      step={step}
                      isChating={isChating}
                      isCurrent={isCurrent}
                    />
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div
            ref={contentRef}
            className="prose prose-sm max-w-none break-words overflow-hidden transition-all duration-300"
          >
            {isUser ? (
              <p className="whitespace-pre-wrap break-words">{renderContent(content)}</p>
            ) : (
              <div className="break-words overflow-x-auto">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeHighlight]}
                  components={markdownComponents}
                >
                  {isCurrent ? renderContent(content) : content}
                </ReactMarkdown>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
