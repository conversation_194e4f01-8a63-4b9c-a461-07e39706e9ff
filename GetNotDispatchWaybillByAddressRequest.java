
/**
 * Autogenerated by Thrift Compiler (0.8.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.sankuai.osg.logistics.tdtc.tservice.tws.waybill;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @TypeDoc(
 *   description = "GetNotDispatchWaybillByAddressRequest"
 * )
 */
public class GetNotDispatchWaybillByAddressRequest implements org.apache.thrift.TBase<GetNotDispatchWaybillByAddressRequest, GetNotDispatchWaybillByAddressRequest._Fields>, java.io.Serializable, Cloneable {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GetNotDispatchWaybillByAddressRequest");

  private static final org.apache.thrift.protocol.TField ADDRESS_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("addressCode", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField QUALITY_INSPECTION_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("qualityInspectionType", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField DATE_PERIOD_FIELD_DESC = new org.apache.thrift.protocol.TField("datePeriod", org.apache.thrift.protocol.TType.STRUCT, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new GetNotDispatchWaybillByAddressRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new GetNotDispatchWaybillByAddressRequestTupleSchemeFactory());
  }

  /**
   * @FieldDoc(
   *   description = "地址编码 等同于外部系统poiId",
   *   name = "addressCode"
   * )
   * 
   */
  public String addressCode; // optional
  /**
   * @FieldDoc(
   *   description = "质检类型 0:水产质检 1:水产巡检",
   *   name = "qualityInspectionType"
   * )
   * 
   */
  public int qualityInspectionType; // optional
  /**
   * @FieldDoc(
   *   description = "时间范围  格式yyyy-MM-dd HH:mm:ss",
   *   name = "datePeriod"
   * )
   * 
   */
  public com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod datePeriod; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * @FieldDoc(
     *   description = "地址编码 等同于外部系统poiId",
     *   name = "addressCode"
     * )
     * 
     */
    ADDRESS_CODE((short)1, "addressCode"),
    /**
     * @FieldDoc(
     *   description = "质检类型 0:水产质检 1:水产巡检",
     *   name = "qualityInspectionType"
     * )
     * 
     */
    QUALITY_INSPECTION_TYPE((short)2, "qualityInspectionType"),
    /**
     * @FieldDoc(
     *   description = "时间范围  格式yyyy-MM-dd HH:mm:ss",
     *   name = "datePeriod"
     * )
     * 
     */
    DATE_PERIOD((short)3, "datePeriod");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ADDRESS_CODE
          return ADDRESS_CODE;
        case 2: // QUALITY_INSPECTION_TYPE
          return QUALITY_INSPECTION_TYPE;
        case 3: // DATE_PERIOD
          return DATE_PERIOD;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __QUALITYINSPECTIONTYPE_ISSET_ID = 0;
  private BitSet __isset_bit_vector = new BitSet(1);
  private _Fields optionals[] = {_Fields.ADDRESS_CODE,_Fields.QUALITY_INSPECTION_TYPE,_Fields.DATE_PERIOD};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ADDRESS_CODE, new org.apache.thrift.meta_data.FieldMetaData("addressCode", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.QUALITY_INSPECTION_TYPE, new org.apache.thrift.meta_data.FieldMetaData("qualityInspectionType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.DATE_PERIOD, new org.apache.thrift.meta_data.FieldMetaData("datePeriod", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod.class)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetNotDispatchWaybillByAddressRequest.class, metaDataMap);
  }

  public GetNotDispatchWaybillByAddressRequest() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GetNotDispatchWaybillByAddressRequest(GetNotDispatchWaybillByAddressRequest other) {
    __isset_bit_vector.clear();
    __isset_bit_vector.or(other.__isset_bit_vector);
    if (other.isSetAddressCode()) {
      this.addressCode = other.addressCode;
    }
    this.qualityInspectionType = other.qualityInspectionType;
    if (other.isSetDatePeriod()) {
      this.datePeriod = new com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod(other.datePeriod);
    }
  }

  public GetNotDispatchWaybillByAddressRequest deepCopy() {
    return new GetNotDispatchWaybillByAddressRequest(this);
  }

  @Override
  public void clear() {
    this.addressCode = null;
    setQualityInspectionTypeIsSet(false);
    this.qualityInspectionType = 0;
    this.datePeriod = null;
  }

  /**
   * @FieldDoc(
   *   description = "地址编码 等同于外部系统poiId",
   *   name = "addressCode"
   * )
   * 
   */
  public String getAddressCode() {
    return this.addressCode;
  }

  /**
   * @FieldDoc(
   *   description = "地址编码 等同于外部系统poiId",
   *   name = "addressCode"
   * )
   * 
   */
  public GetNotDispatchWaybillByAddressRequest setAddressCode(String addressCode) {
    this.addressCode = addressCode;
    return this;
  }

  public void unsetAddressCode() {
    this.addressCode = null;
  }

  /** Returns true if field addressCode is set (has been assigned a value) and false otherwise */
  public boolean isSetAddressCode() {
    return this.addressCode != null;
  }

  public void setAddressCodeIsSet(boolean value) {
    if (!value) {
      this.addressCode = null;
    }
  }

  /**
   * @FieldDoc(
   *   description = "质检类型 0:水产质检 1:水产巡检",
   *   name = "qualityInspectionType"
   * )
   * 
   */
  public int getQualityInspectionType() {
    return this.qualityInspectionType;
  }

  /**
   * @FieldDoc(
   *   description = "质检类型 0:水产质检 1:水产巡检",
   *   name = "qualityInspectionType"
   * )
   * 
   */
  public GetNotDispatchWaybillByAddressRequest setQualityInspectionType(int qualityInspectionType) {
    this.qualityInspectionType = qualityInspectionType;
    setQualityInspectionTypeIsSet(true);
    return this;
  }

  public void unsetQualityInspectionType() {
    __isset_bit_vector.clear(__QUALITYINSPECTIONTYPE_ISSET_ID);
  }

  /** Returns true if field qualityInspectionType is set (has been assigned a value) and false otherwise */
  public boolean isSetQualityInspectionType() {
    return __isset_bit_vector.get(__QUALITYINSPECTIONTYPE_ISSET_ID);
  }

  public void setQualityInspectionTypeIsSet(boolean value) {
    __isset_bit_vector.set(__QUALITYINSPECTIONTYPE_ISSET_ID, value);
  }

  /**
   * @FieldDoc(
   *   description = "时间范围  格式yyyy-MM-dd HH:mm:ss",
   *   name = "datePeriod"
   * )
   * 
   */
  public com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod getDatePeriod() {
    return this.datePeriod;
  }

  /**
   * @FieldDoc(
   *   description = "时间范围  格式yyyy-MM-dd HH:mm:ss",
   *   name = "datePeriod"
   * )
   * 
   */
  public GetNotDispatchWaybillByAddressRequest setDatePeriod(com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod datePeriod) {
    this.datePeriod = datePeriod;
    return this;
  }

  public void unsetDatePeriod() {
    this.datePeriod = null;
  }

  /** Returns true if field datePeriod is set (has been assigned a value) and false otherwise */
  public boolean isSetDatePeriod() {
    return this.datePeriod != null;
  }

  public void setDatePeriodIsSet(boolean value) {
    if (!value) {
      this.datePeriod = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ADDRESS_CODE:
      if (value == null) {
        unsetAddressCode();
      } else {
        setAddressCode((String)value);
      }
      break;

    case QUALITY_INSPECTION_TYPE:
      if (value == null) {
        unsetQualityInspectionType();
      } else {
        setQualityInspectionType((Integer)value);
      }
      break;

    case DATE_PERIOD:
      if (value == null) {
        unsetDatePeriod();
      } else {
        setDatePeriod((com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ADDRESS_CODE:
      return getAddressCode();

    case QUALITY_INSPECTION_TYPE:
      return Integer.valueOf(getQualityInspectionType());

    case DATE_PERIOD:
      return getDatePeriod();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ADDRESS_CODE:
      return isSetAddressCode();
    case QUALITY_INSPECTION_TYPE:
      return isSetQualityInspectionType();
    case DATE_PERIOD:
      return isSetDatePeriod();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof GetNotDispatchWaybillByAddressRequest)
      return this.equals((GetNotDispatchWaybillByAddressRequest)that);
    return false;
  }

  public boolean equals(GetNotDispatchWaybillByAddressRequest that) {
    if (that == null)
      return false;

    boolean this_present_addressCode = true && this.isSetAddressCode();
    boolean that_present_addressCode = true && that.isSetAddressCode();
    if (this_present_addressCode || that_present_addressCode) {
      if (!(this_present_addressCode && that_present_addressCode))
        return false;
      if (!this.addressCode.equals(that.addressCode))
        return false;
    }

    boolean this_present_qualityInspectionType = true && this.isSetQualityInspectionType();
    boolean that_present_qualityInspectionType = true && that.isSetQualityInspectionType();
    if (this_present_qualityInspectionType || that_present_qualityInspectionType) {
      if (!(this_present_qualityInspectionType && that_present_qualityInspectionType))
        return false;
      if (this.qualityInspectionType != that.qualityInspectionType)
        return false;
    }

    boolean this_present_datePeriod = true && this.isSetDatePeriod();
    boolean that_present_datePeriod = true && that.isSetDatePeriod();
    if (this_present_datePeriod || that_present_datePeriod) {
      if (!(this_present_datePeriod && that_present_datePeriod))
        return false;
      if (!this.datePeriod.equals(that.datePeriod))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return 0;
  }

  public int compareTo(GetNotDispatchWaybillByAddressRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;
    GetNotDispatchWaybillByAddressRequest typedOther = (GetNotDispatchWaybillByAddressRequest)other;

    lastComparison = Boolean.valueOf(isSetAddressCode()).compareTo(typedOther.isSetAddressCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAddressCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.addressCode, typedOther.addressCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetQualityInspectionType()).compareTo(typedOther.isSetQualityInspectionType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetQualityInspectionType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.qualityInspectionType, typedOther.qualityInspectionType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDatePeriod()).compareTo(typedOther.isSetDatePeriod());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDatePeriod()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.datePeriod, typedOther.datePeriod);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("GetNotDispatchWaybillByAddressRequest(");
    boolean first = true;

    if (isSetAddressCode()) {
      sb.append("addressCode:");
      if (this.addressCode == null) {
        sb.append("null");
      } else {
        sb.append(this.addressCode);
      }
      first = false;
    }
    if (isSetQualityInspectionType()) {
      if (!first) sb.append(", ");
      sb.append("qualityInspectionType:");
      sb.append(this.qualityInspectionType);
      first = false;
    }
    if (isSetDatePeriod()) {
      if (!first) sb.append(", ");
      sb.append("datePeriod:");
      if (this.datePeriod == null) {
        sb.append("null");
      } else {
        sb.append(this.datePeriod);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bit_vector = new BitSet(1);
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GetNotDispatchWaybillByAddressRequestStandardSchemeFactory implements SchemeFactory {
    public GetNotDispatchWaybillByAddressRequestStandardScheme getScheme() {
      return new GetNotDispatchWaybillByAddressRequestStandardScheme();
    }
  }

  private static class GetNotDispatchWaybillByAddressRequestStandardScheme extends StandardScheme<GetNotDispatchWaybillByAddressRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GetNotDispatchWaybillByAddressRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ADDRESS_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.addressCode = iprot.readString();
              struct.setAddressCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // QUALITY_INSPECTION_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.qualityInspectionType = iprot.readI32();
              struct.setQualityInspectionTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // DATE_PERIOD
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.datePeriod = new com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod();
              struct.datePeriod.read(iprot);
              struct.setDatePeriodIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GetNotDispatchWaybillByAddressRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.addressCode != null) {
        if (struct.isSetAddressCode()) {
          oprot.writeFieldBegin(ADDRESS_CODE_FIELD_DESC);
          oprot.writeString(struct.addressCode);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetQualityInspectionType()) {
        oprot.writeFieldBegin(QUALITY_INSPECTION_TYPE_FIELD_DESC);
        oprot.writeI32(struct.qualityInspectionType);
        oprot.writeFieldEnd();
      }
      if (struct.datePeriod != null) {
        if (struct.isSetDatePeriod()) {
          oprot.writeFieldBegin(DATE_PERIOD_FIELD_DESC);
          struct.datePeriod.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GetNotDispatchWaybillByAddressRequestTupleSchemeFactory implements SchemeFactory {
    public GetNotDispatchWaybillByAddressRequestTupleScheme getScheme() {
      return new GetNotDispatchWaybillByAddressRequestTupleScheme();
    }
  }

  private static class GetNotDispatchWaybillByAddressRequestTupleScheme extends TupleScheme<GetNotDispatchWaybillByAddressRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GetNotDispatchWaybillByAddressRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAddressCode()) {
        optionals.set(0);
      }
      if (struct.isSetQualityInspectionType()) {
        optionals.set(1);
      }
      if (struct.isSetDatePeriod()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetAddressCode()) {
        oprot.writeString(struct.addressCode);
      }
      if (struct.isSetQualityInspectionType()) {
        oprot.writeI32(struct.qualityInspectionType);
      }
      if (struct.isSetDatePeriod()) {
        struct.datePeriod.write(oprot);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GetNotDispatchWaybillByAddressRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.addressCode = iprot.readString();
        struct.setAddressCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.qualityInspectionType = iprot.readI32();
        struct.setQualityInspectionTypeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.datePeriod = new com.sankuai.osg.logistics.tdtc.tservice.common.TDatePeriod();
        struct.datePeriod.read(iprot);
        struct.setDatePeriodIsSet(true);
      }
    }
  }

}
