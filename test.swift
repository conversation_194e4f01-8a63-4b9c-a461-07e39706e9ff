//
//  ContentView.swift
//  easyrecord
//
//  Created by 闪电侠 on 2025/6/6.
//

import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var items: [Item]
    @StateObject private var screenRecorder = ScreenRecorder()

    var body: some View {
        NavigationSplitView {
            List {
                ForEach(items) { item in
                    NavigationLink {
                        Text("Item at $item.timestamp, format: Date.FormatStyle(date: .numeric, time: .standard))")
                    } label: {
                        Text(item.timestamp, format: Date.FormatStyle(date: .numeric, time: .standard))
                    }
                }
                .onDelete(perform: deleteItems)
            }
            .navigationSplitViewColumnWidth(min: 180, ideal: 200)
            .toolbar {
                ToolbarItem {
                    Button(action: addItem) {
                        Label("Add Item", systemImage: "plus")
                    }
                }
            }
        } detail: {
            VStack(spacing: 20) {
                Text("屏幕录制")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                if screenRecorder.isRecording {
                    VStack(spacing: 10) {
                        Text("正在录制...")
                            .font(.title2)
                            .foregroundColor(.red)

                        Text("录制时间: \(formatTime(screenRecorder.recordingTime))")
                            .font(.title3)
                            .foregroundColor(.secondary)

                        Button("停止录制") {
                            Task {
                                await screenRecorder.stopRecording()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        .tint(.red)
                    }
                } else {
                    VStack(spacing: 10) {
                        Text("点击开始录制屏幕")
                            .font(.title3)
                            .foregroundColor(.secondary)

                        Button("开始录制") {
                            Task {
                                await screenRecorder.startRecording()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        .tint(.green)
                    }
                }

                Text("录制的视频将保存到桌面")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top)
            }
            .padding()
        }
    }

    private func addItem() {
        withAnimation {
            let newItem = Item(timestamp: Date())
            modelContext.insert(newItem)
        }
    }

    private func deleteItems(offsets: IndexSet) {
        withAnimation {
            for index in offsets {
                modelContext.delete(items[index])
            }
        }
    }

    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

#Preview {
    ContentView()
        .modelContainer(for: Item.self, inMemory: true)
}