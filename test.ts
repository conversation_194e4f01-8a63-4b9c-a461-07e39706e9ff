let originalCode = "/**\n * 构建前端waimaiContent数据为后端格式\n * 将 {poiName} 替换为 $POI#\n */\nexport function buildWaimaiContentForBackend(waimaiContent) {\n  if (!waimaiContent) return '';\n  return waimaiContent.replace(/\\{poiName\\}/g, '$$POI#$');\n}"

let old_string = "/**\n * 构建前端waimaiContent数据为后端格式\n * 将 {poiName} 替换为 $POI#\n */\nexport function buildWaimaiContentForBackend(waimaiContent) {\n  if (!waimaiContent) return '';\n  return waimaiContent.replace(/\\{poiName\\}/g, '$$POI#$');\n}"
let new_string = "/**\n * 构建前端waimaiContent数据为后端格式\n * 将 {poiName} 替换为 $POI#\n */\nexport function buildWaimaiContentForBackend(waimaiContent) {\n  if (!waimaiContent) return '';\n  return waimaiContent.replace(/\\{poiName\\}/g, '$$POI#$');\n}\n\n/**\n * 解析个人罐筹款中文案，去掉固定前缀保留用户输入部分\n */\nexport function parsePrivateUnuseTemplate(fullTemplate) {\n  if (!fullTemplate) return '';\n  const prefix = FIXED_TEXT_CONFIG.DTS_PRIVATE_UNUSE_PREFIX;\n  if (fullTemplate.startsWith(prefix)) {\n    const userInput = fullTemplate.substring(prefix.length);\n    // 将样式化的POI占位符还原为用户输入格式\n    return userInput.replace(/<b style=\"color:rgba\\(0,0,0,0\\.6\\);\">\\{poiName\\}<\\/b>/g, '<b>{poiName}</b>');\n  }\n  return fullTemplate;\n}\n\n/**\n * 解析个人罐执行中文案，去掉固定前缀保留用户输入部分\n */\nexport function parsePrivateUsingTemplate(fullTemplate) {\n  if (!fullTemplate) return '';\n  const prefix = FIXED_TEXT_CONFIG.DTS_PRIVATE_USING_PREFIX;\n  if (fullTemplate.startsWith(prefix)) {\n    const userInput = fullTemplate.substring(prefix.length);\n    // 将样式化的POI占位符还原为用户输入格式\n    return userInput.replace(/<b style=\"color:rgba\\(0,0,0,0\\.6\\);\">\\{poiName\\}<\\/b>/g, '<b>{poiName}</b>');\n  }\n  return fullTemplate;\n}\n\n/**\n * 解析个人罐执行完成文案，去掉固定前缀保留用户输入部分\n */\nexport function parsePrivateUsedTemplate(fullTemplate) {\n  if (!fullTemplate) return '';\n  const prefix = FIXED_TEXT_CONFIG.DTS_PRIVATE_USED_PREFIX;\n  if (fullTemplate.startsWith(prefix)) {\n    const userInput = fullTemplate.substring(prefix.length);\n    // 将样式化的POI占位符还原为用户输入格式\n    return userInput.replace(/<b style=\"color:rgba\\(0,0,0,0\\.6\\);\">\\{poiName\\}<\\/b>/g, '<b>{poiName}</b>');\n  }\n  return fullTemplate;\n}\n\n/**\n * 解析公共罐执行中文案，去掉固定前缀保留用户输入部分\n */\nexport function parsePublicUsingTemplate(fullTemplate) {\n  if (!fullTemplate) return '';\n  const prefix = FIXED_TEXT_CONFIG.DTS_PUBLIC_USING_PREFIX;\n  if (fullTemplate.startsWith(prefix)) {\n    return fullTemplate.substring(prefix.length);\n  }\n  return fullTemplate;\n}\n\n/**\n * 解析公共罐执行完成文案，去掉固定前缀保留用户输入部分\n */\nexport function parsePublicUsedTemplate(fullTemplate) {\n  if (!fullTemplate) return '';\n  const prefix = FIXED_TEXT_CONFIG.DTS_PUBLIC_USED_PREFIX;\n  if (fullTemplate.startsWith(prefix)) {\n    return fullTemplate.substring(prefix.length);\n  }\n  return fullTemplate;\n}\n\n/**\n * 解析DTS卡片文案，从数组中提取用户输入部分\n */\nexport function parseDtsContentTemplate(templateArray) {\n  if (!templateArray || !Array.isArray(templateArray) || templateArray.length === 0) return '';\n  const firstTemplate = templateArray[0];\n  const prefix = FIXED_TEXT_CONFIG.DTS_CONTENT_FIRST_PREFIX;\n  if (firstTemplate && firstTemplate.startsWith(prefix)) {\n    const userInput = firstTemplate.substring(prefix.length);\n    // 将{poi}还原为{poiName}\n    return userInput.replace(/\\{poi\\}/g, '{poiName}');\n  }\n  return firstTemplate || '';\n}\n\n/**\n * 解析证书正文，去掉固定前缀保留用户输入部分\n */\nexport function parseCertificateContent(fullContent) {\n  if (!fullContent) return '';\n  const prefix = FIXED_TEXT_CONFIG.CERTIFICATE_CONTENT_PREFIX;\n  if (fullContent.startsWith(prefix)) {\n    return fullContent.substring(prefix.length);\n  }\n  return fullContent;\n}\n\n/**\n * 解析证书左下角文案，去掉固定前缀保留用户输入部分\n */\nexport function parseCertificateLeftLowerText(fullText) {\n  if (!fullText) return '';\n  const prefix = FIXED_TEXT_CONFIG.CERTIFICATE_LEFT_LOWER_PREFIX;\n  if (fullText.startsWith(prefix)) {\n    return fullText.substring(prefix.length);\n  }\n  return fullText;\n}"

console.log(`\noriginal code --------> \n${originalCode}`)
console.log(`\nold string --------> \n${old_string}`)
console.log(`\nnew string --------> \n${new_string}`)

let newCode = originalCode.replace(old_string, new_string)
console.log(`\nnew code --------> \n${newCode}`)




// const paragraph = "I think Ruth's dog is cuter than your dog!";

// console.log(paragraph.replace("Ruth's", "my"));
// // Expected output: "I think my dog is cuter than your dog!"

// const regex = /dog/i;
// console.log(paragraph.replace(regex, "ferret"));
// // Expected output: "I think Ruth's ferret is cuter than your dog!"
