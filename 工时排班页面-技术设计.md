## 2. 改动点分析
### 2.1 页面生命周期改动




#### onLoad改动点
- 【新增】执行动作：初始化页面状态，解析URL参数，获取Region信息
- 【新增】接口调用：获取工时班次列表
- 【新增】数据处理：初始化表单数据，包括Region、排班日期、人员列表和工时班次

#### onShow改动点
- 【新增】执行动作：检查页面状态，恢复之前的选择（如有）
- 【新增】数据处理：处理从其他页面返回的数据（如人员选择、班次选择）

### 2.2 用户交互改动
#### 【新增】选择排班日期
- 触发条件：用户点击排班日期输入框
- 执行动作：打开日期选择组件，支持选择连续日期或单个日期
- 接口调用：无
- 状态变化：更新排班日期字段
- 页面跳转：无

#### 【新增】选择人员
- 触发条件：用户点击选择人员按钮
- 执行动作：跳转到人员选择页面
- 接口调用：无
- 状态变化：无
- 页面跳转：跳转到人员选择页面

#### 【新增】选择工时班次
- 触发条件：用户点击"选择工时班次"按钮
- 执行动作：跳转到班次选择页面
- 接口调用：无
- 状态变化：无
- 页面跳转：跳转到班次选择页面

#### 【新增】提交排班
- 触发条件：用户点击"提交"按钮
- 执行动作：校验表单数据，提交工时排班信息
- 接口调用：调用创建工时排班接口
- 状态变化：提交成功后清空表单数据
- 页面跳转：提交成功后返回排班管理页面

#### 【新增】班次管理入口
- 触发条件：用户点击页面右上角"班次管理"按钮
- 执行动作：跳转到班次管理页面
- 接口调用：无
- 状态变化：无
- 页面跳转：跳转到班次管理页面

## 3. 架构设计

### 3.0 页面出入参
- 入参：
  - regionId: string "地区ID"
  - businessLine: number "业务线"
- 出参：无

### 3.1 类图设计
```plantuml
@startuml
class WorkTimeSchedulePage {
    -uiState: WorkTimeScheduleUIState "UI状态"
    -viewModel: WorkTimeScheduleViewModel "视图模型"

    +onLoad(options) "页面加载生命周期"
    +onShow() "页面显示生命周期"
    +onHide() "页面隐藏生命周期"
    +onUnload() "页面卸载生命周期"
    +onSelectDate(e) "选择日期回调"
    +onSelectPersonnel() "选择人员回调"
    +onSelectShift() "选择班次回调"
    +onDeletePerson(e) "删除人员回调"
    +onSubmit() "提交表单回调"
    +onCancel() "取消操作回调"
    +onGoToShiftManage() "前往班次管理回调"
}

class WorkTimeScheduleViewModel {
    -uiState: WorkTimeScheduleUIState "UI状态引用"
    -model: WorkTimeScheduleModel "数据模型"

    +initialize(options) "初始化方法"
    +loadData() "加载数据"
    +selectDate(date) "选择日期"
    +selectPersonnel() "选择人员"
    +selectShift() "选择班次"
    +deletePerson(personId) "删除人员"
    +submit() "提交表单"
    +cancel() "取消操作"
    +goToShiftManage() "前往班次管理"
    -validateForm() "验证表单"
    -processDateSelection(date) "处理日期选择"
    -handleTodaySchedule() "处理今日排班时间"
}

class WorkTimeScheduleUIState {
    +region: object "地区信息"
    +scheduleDate: array "排班日期"
    +personnel: array "选择的人员列表"
    +selectedShift: object "选择的工时班次"
    +isLoading: boolean "是否加载中"
    +submitLoading: boolean "提交按钮加载状态"
    +formValid: boolean "表单是否有效"
    +errorMessage: string "错误信息"
    +todayScheduleTime: string "今日排班开始时间"

    +setRegion(region) "设置地区"
    +setScheduleDate(date) "设置排班日期"
    +setPersonnel(personnel) "设置人员列表"
    +addPerson(person) "添加人员"
    +removePerson(personId) "移除人员"
    +setSelectedShift(shift) "设置选择的班次"
    +setLoading(loading) "设置加载状态"
    +setSubmitLoading(loading) "设置提交加载状态"
    +setFormValid(valid) "设置表单有效状态"
    +setErrorMessage(message) "设置错误信息"
    +setTodayScheduleTime(time) "设置今日排班时间"
    +reset() "重置状态"
}

class WorkTimeScheduleModel {
    +createWorkTimeSchedule(params) "创建工时排班"
    +getWorkTimeShifts(regionId, businessLine) "获取工时班次列表"
    +getPersonnel(regionId, businessLine) "获取人员列表"
}

class DateSelector {
    +date: array "选择的日期"
    +minDate: string "最小可选日期"
    +maxDate: string "最大可选日期"
    +multiple: boolean "是否支持多选"

    +onChange(date) "日期变化回调"
    +onConfirm(date) "确认选择回调"
    +onCancel() "取消选择回调"
}

class PersonnelSelector {
    +selected: array "已选择的人员"
    +regionId: string "地区ID"
    +businessLine: number "业务线"

    +onSelect(personnel) "选择回调"
    +onDelete(personId) "删除回调"
}

class ShiftInfoCard {
    +shift: object "班次信息"
    +selected: boolean "是否选中"

    +onClick() "点击回调"
}

WorkTimeSchedulePage --> WorkTimeScheduleViewModel: "使用"
WorkTimeScheduleViewModel --> WorkTimeScheduleUIState: "更新"
WorkTimeScheduleViewModel --> WorkTimeScheduleModel: "调用"
WorkTimeSchedulePage --> DateSelector: "使用"
WorkTimeSchedulePage --> PersonnelSelector: "使用"
WorkTimeSchedulePage --> ShiftInfoCard: "使用"
@enduml
```

### 3.2 时序图设计
```plantuml
@startuml
participant User as "用户"
participant Page as "工时排班页面"
participant ViewModel as "视图模型"
participant UIState as "UI状态"
participant Model as "数据模型"
participant API as "API"
participant Components as "组件"

== 页面加载阶段 ==
User -> Page: 进入工时排班页面
activate Page
Page -> Page: onLoad(options)
Page -> ViewModel: initialize(options)
activate ViewModel
ViewModel -> UIState: 初始化UI状态
ViewModel -> Model: getWorkTimeShifts(regionId, businessLine)
Model -> API: 请求工时班次列表
API --> Model: 返回班次数据
Model --> ViewModel: 返回班次数据
ViewModel -> UIState: 更新UI状态
ViewModel --> Page: 初始化完成
deactivate ViewModel
Page --> User: 渲染页面内容
deactivate Page

== 选择排班日期 ==
User -> Page: 点击排班日期输入框
activate Page
Page -> Components: 显示日期选择组件
Components --> User: 展示日期选择器
User -> Components: 选择日期
Components -> Page: onSelectDate(date)
Page -> ViewModel: selectDate(date)
activate ViewModel
ViewModel -> ViewModel: processDateSelection(date)
ViewModel -> ViewModel: handleTodaySchedule()
ViewModel -> UIState: setScheduleDate(date)
ViewModel -> UIState: setTodayScheduleTime(time)
UIState --> ViewModel: 更新完成
ViewModel --> Page: 日期选择处理完成
deactivate ViewModel
Page --> User: 更新页面显示
deactivate Page

== 选择人员 ==
User -> Page: 点击选择人员按钮
activate Page
Page -> ViewModel: selectPersonnel()
ViewModel -> Page: 跳转到人员选择页面
Page --> User: 打开人员选择页面
User -> Components: 选择人员
Components -> Page: 返回选择结果
Page -> ViewModel: 处理选择结果
ViewModel -> UIState: setPersonnel(personnel)
UIState --> Page: 更新UI状态
Page --> User: 更新页面显示
deactivate Page

== 选择工时班次 ==
User -> Page: 点击选择工时班次按钮
activate Page
Page -> ViewModel: selectShift()
ViewModel -> Page: 跳转到班次选择页面
Page --> User: 打开班次选择页面
User -> Components: 选择班次
Components -> Page: 返回选择结果
Page -> ViewModel: 处理选择结果
ViewModel -> UIState: setSelectedShift(shift)
UIState --> Page: 更新UI状态
Page --> User: 更新页面显示
deactivate Page

== 提交排班 ==
User -> Page: 点击提交按钮
activate Page
Page -> ViewModel: submit()
activate ViewModel
ViewModel -> ViewModel: validateForm()
alt 表单验证通过
    ViewModel -> UIState: setSubmitLoading(true)
    ViewModel -> Model: createWorkTimeSchedule(params)
    Model -> API: 提交排班数据
    API --> Model: 返回结果
    Model --> ViewModel: 返回结果
    ViewModel -> UIState: setSubmitLoading(false)
    alt 提交成功
        ViewModel -> UIState: reset()
        ViewModel -> Page: 返回上一页
        Page --> User: 返回排班管理页面
    else 提交失败
        ViewModel -> UIState: setErrorMessage(message)
        UIState --> Page: 更新UI状态
        Page --> User: 显示错误提示
    end
else 表单验证失败
    ViewModel -> UIState: setErrorMessage(message)
    UIState --> Page: 更新UI状态
    Page --> User: 显示验证错误
end
deactivate ViewModel
deactivate Page

== 班次管理入口 ==
User -> Page: 点击班次管理按钮
activate Page
Page -> ViewModel: goToShiftManage()
ViewModel -> Page: 跳转到班次管理页面
Page --> User: 打开班次管理页面
deactivate Page
@enduml
```

### 3.3 流程图设计
```plantuml
@startuml
start
:用户进入工时排班页面;
:初始化页面状态;
:加载Region信息;
:获取工时班次列表;

repeat
  if (用户点击排班日期?) then (是)
    :显示日期选择组件;
    :用户选择日期;
    if (选择包含今日?) then (是)
      :计算今日排班开始时间;
      :显示今日排班时间提示;
    endif
    :更新排班日期字段;
  elseif (用户点击选择人员?) then (是)
    :跳转到人员选择页面;
    :用户选择人员;
    :返回工时排班页面;
    :更新人员列表;
  elseif (用户点击删除人员?) then (是)
    :从人员列表中移除该人员;
    :更新人员列表显示;
  elseif (用户点击选择工时班次?) then (是)
    :跳转到班次选择页面;
    :用户选择班次;
    :返回工时排班页面;
    :更新工时班次字段;
  elseif (用户点击班次管理?) then (是)
    :跳转到班次管理页面;
  elseif (用户点击提交?) then (是)
    :验证表单数据;
    if (验证通过?) then (是)
      :调用创建工时排班接口;
      if (提交成功?) then (是)
        :显示成功提示;
        :返回排班管理页面;
        stop
      else (否)
        :显示提交失败提示;
      endif
    else (否)
      :显示验证错误提示;
    endif
  elseif (用户点击取消?) then (是)
    :返回排班管理页面;
    stop
  endif
repeat while (用户继续操作?) is (是)
stop
@enduml
```

### 3.4 组件设计
| 组件名 | props | event事件 | triggerEvent |
|--------|-------|-----------|-------------|
| DateSelector | date: array "选择的日期"<br>minDate: string "最小可选日期"<br>maxDate: string "最大可选日期"<br>multiple: boolean "是否支持多选" | onChange: function "日期变化回调"<br>onConfirm: function "确认选择回调"<br>onCancel: function "取消选择回调" | change: "日期变化"<br>confirm: "确认选择"<br>cancel: "取消选择" |
| PersonnelSelector | selected: array "已选择的人员"<br>regionId: string "地区ID"<br>businessLine: number "业务线" | onSelect: function "选择回调"<br>onDelete: function "删除回调" | select: "选择人员"<br>delete: "删除人员" |
| ShiftInfoCard | shift: object "班次信息"<br>selected: boolean "是否选中" | onClick: function "点击回调" | click: "点击班次" |

## 4. 测试范围
### 4.1 功能测试
| 测试场景 | 前置条件 | 测试步骤 | 预期结果 |
|---------|---------|---------|---------|
| 页面初始化 | 用户有排班权限 | 1. 进入工时排班页面 | 页面正确加载，显示Region、排班日期、选择人员、选择工时班次等字段 |
| 选择排班日期 | 页面已加载 | 1. 点击排班日期输入框<br>2. 在日期选择器中选择日期<br>3. 点击确认 | 排班日期字段更新为选择的日期 |
| 选择包含今日的排班日期 | 页面已加载 | 1. 点击排班日期输入框<br>2. 选择包含今日的日期<br>3. 点击确认 | 排班日期字段更新，显示今日排班开始时间提示 |
| 选择人员 | 页面已加载 | 1. 点击选择人员按钮<br>2. 在人员选择页面选择人员<br>3. 确认选择 | 返回工时排班页面，显示已选择的人员列表 |
| 删除人员 | 已选择人员 | 1. 点击人员列表中某人员右侧的删除按钮 | 该人员从列表中移除 |
| 选择工时班次 | 页面已加载 | 1. 点击选择工时班次按钮<br>2. 在班次选择页面选择班次<br>3. 确认选择 | 返回工时排班页面，显示已选择的班次信息 |
| 提交排班-成功 | 已填写所有必填字段 | 1. 点击提交按钮 | 显示提交成功提示，返回排班管理页面 |
| 提交排班-表单验证 | 未填写完所有必填字段 | 1. 点击提交按钮 | 显示表单验证错误提示 |
| 班次管理入口 | 页面已加载 | 1. 点击页面右上角班次管理按钮 | 跳转到班次管理页面 |

### 4.2 边界测试
| 测试场景 | 前置条件 | 测试步骤 | 预期结果 |
|---------|---------|---------|---------|
| 选择过去日期 | 页面已加载 | 1. 点击排班日期输入框<br>2. 尝试选择过去日期 | 过去日期不可选或选择后提示错误 |
| 选择大量人员 | 页面已加载 | 1. 点击选择人员按钮<br>2. 选择大量人员(>50人) | 正确显示所有选择的人员，不出现性能问题 |
| 无可用班次 | 当前region下无工时班次 | 1. 进入工时排班页面<br>2. 点击选择工时班次按钮 | 显示"暂无可用班次"提示 |

### 4.3 异常测试
| 测试场景 | 前置条件 | 测试步骤 | 预期结果 |
|---------|---------|---------|---------|
| 网络异常-加载 | 网络不稳定 | 1. 在网络不稳定状态下进入页面 | 显示加载失败提示，提供重试选项 |
| 网络异常-提交 | 网络不稳定，已填写所有必填字段 | 1. 点击提交按钮 | 显示提交失败提示，提供重试选项 |
| 接口异常-创建排班 | 接口返回错误 | 1. 填写表单<br>2. 点击提交按钮 | 显示具体错误信息，不跳转页面 |
| 快速点击提交按钮 | 已填写所有必填字段 | 1. 快速多次点击提交按钮 | 只发送一次请求，按钮显示加载状态防止重复提交 |
