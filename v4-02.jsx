import React from 'react';
import FileIcon from '@/pages/components/FileIcon.jsx';


export function ChatLayoutLeft(ResizeContainer, chatContainerWidth, chatContainerNode) {
    return (
        // 左侧聊天窗口
        <div className='relative h-full flex-shrink-0 flex'>
            {/* 聊天窗口 */}
            <div className='h-full overflow-hidden flex-grow' style={{ width: `${chatContainerWidth}px` }}>
                <div className='h-full overflow-auto'>
                    {/* 聊天内容 */}
                    {chatContainerNode}
                </div>
            </div>

            {/* 调整宽度手柄 */}
            {ResizeContainer}
        </div>
    );
}