package com.sankuai.fastexception.server.service.autoInject;


import com.sankuai.fastexception.server.dao.domain.AutoInjectPlan;
import com.sankuai.fastexception.server.enums.AutoInjectPlanStatusEnum;
import com.sankuai.fastexception.server.model.autoInjectService.AutoInjectParam;
import com.sankuai.fastexception.server.model.autoInjectService.AutoInjectPlanDto;
import com.sankuai.fastexception.server.model.paramRecommend.ParamExcuteBaseInfo;
import com.sankuai.fastexception.server.repository.impl.AutoInjectPlanRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AsyncAutoInjectService {

    @Autowired
    AutoInjectService autoInjectService;

    @Autowired
    AutoInjectPlanRepository autoInjectPlanRepository;

    public long asyncBatchExcuteAutoInjectTest(AutoInjectParam autoInjectParam) {

        ParamExcuteBaseInfo paramExcuteBaseInfo = autoInjectParam.getParamExcuteBaseInfo();

        //创建任务
        AutoInjectPlan autoInjectPlan = autoInjectPlanRepository.insertAutoInjectPlan(coverAutoInjectPlanDto(paramExcuteBaseInfo));
        Long autoInjectPlanId = autoInjectPlan.getId();

        autoInjectService.batchExcuteAutoInjectTest(autoInjectParam, autoInjectPlanId);

        return autoInjectPlanId;
    }


    public AutoInjectPlanDto coverAutoInjectPlanDto(ParamExcuteBaseInfo paramExcuteBaseInfo) {
        AutoInjectPlanDto autoInjectPlanDto = new AutoInjectPlanDto();
        autoInjectPlanDto.setAppkey(paramExcuteBaseInfo.getAppkey());
        autoInjectPlanDto.setSupplyProgramId(Long.valueOf(paramExcuteBaseInfo.getSupplyProgramId()));
        autoInjectPlanDto.setBranch(paramExcuteBaseInfo.getBranch());
        autoInjectPlanDto.setMethodType(paramExcuteBaseInfo.getMethodType());
        autoInjectPlanDto.setIp(paramExcuteBaseInfo.getIp());
        autoInjectPlanDto.setInterfaceName(paramExcuteBaseInfo.getInterfaceName());
        autoInjectPlanDto.setMethodPath(paramExcuteBaseInfo.getPackageName() + "." + paramExcuteBaseInfo.getMethodName());
        autoInjectPlanDto.setPlanStatus((byte) AutoInjectPlanStatusEnum.INIT_EXECUTE.getIndex());
        autoInjectPlanDto.setCreator(paramExcuteBaseInfo.getOpUser());
        autoInjectPlanDto.setRequestParam(removeSpaces(paramExcuteBaseInfo.getParam()));
        return autoInjectPlanDto;
    }

    /**
     * 移除字符串中的所有空格
     * @param input 输入的字符串
     * @return 移除空格后的字符串
     */
    public static String removeSpaces(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll(" ", "");
    }
}