package com.meituan.pay.munifybalance.domain.bean;

import com.meituan.pay.munifybalance.domain.enums.AcceptStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Setter
@Getter
@Data
public class WithdrawAcceptRecord {
    // Getters and Setters
    private Long id;
    private Long tradeNo;
    private Long bizAdminId; // 账号ID
    private Long totalAmount; // 总提现金额
    private Long settleAmount; // 结算提现金额
    private Long walletAmount; // 钱包提现金额
    private AcceptStatus tradeStatus; // 单据状态
    private String failMsg; // 失败信息
    private Timestamp addTime; // 创建时间
    private Timestamp updateTime; // 更新时间

    @Override
    public String toString() {
        return "WithdrawAcceptRecord{" +
                "id=" + id +
                ", bizAdminId=" + bizAdminId +
                ", totalAmount=" + totalAmount +
                ", settleAmount=" + settleAmount +
                ", walletAmount=" + walletAmount +
                ", tradeStatus=" + tradeStatus +
                '}';
    }
}
