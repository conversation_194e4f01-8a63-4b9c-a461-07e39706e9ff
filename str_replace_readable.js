/**
 * Asynchronously handles the 'str_replace' command.
 * This function iterates through a series of replacement requests, applies them to the file content,
 * and returns a response detailing the outcome of each replacement.
 *
 * @param {string} filePath - The path to the file being modified.
 * @param {object} file - An object representing the file, containing its contents.
 * @param {Array<object>} replacements - An array of replacement objects.
 * @param {object} context - The context of the tool call.
 * @returns {Promise<object>} A prepared tool response.
 */
async function handleStringReplacement(filePath, file, replacements, context) {
    this._logger.debug(`Handling str_replace command for ${filePath}`);

    // Sort replacements to process from the end of the file to the beginning.
    // This avoids line number changes affecting subsequent replacements.
    const sortedReplacements = sortReplacementsByLineNumber(replacements);
    const newlineChar = detectNewlineChar(file.contents);
    const originalContent = normalizeNewlines(file.contents);
    let currentContent = originalContent;
    const results = new Map();

    for (const replacement of sortedReplacements) {
        const overlappingReplacement = findOverlappingReplacement(replacement, sortedReplacements);
        let result;

        if (overlappingReplacement) {
            result = {
                isError: true,
                index: replacement.index,
                oldStr: String(replacement.old_str),
                oldStrStartLineNumber: replacement.old_str_start_line_number,
                oldStrEndLineNumber: replacement.old_str_end_line_number,
                numLinesDiff: 0,
                genMessageFunc: () => generateOverlapErrorMessage(replacement, overlappingReplacement),
            };
        } else if (typeof replacement.old_str === "string" && typeof replacement.new_str === "string") {
            result = this.performSingleStringReplacement(
                filePath,
                currentContent,
                replacement.old_str,
                replacement.new_str,
                replacement.index,
                replacement.old_str_start_line_number,
                replacement.old_str_end_line_number,
            );
        } else {
            result = this.performSingleJsonReplacement(
                filePath,
                currentContent,
                replacement.old_str, // This will be a JSON object
                replacement.new_str, // This will be a JSON object
                replacement.index,
                replacement.old_str_start_line_number,
                replacement.old_str_end_line_number,
            );
        }

        results.set(replacement.index, result);

        if (!result.isError && result.newContent !== undefined) {
            currentContent = result.newContent;
        }
    }

    return this.prepareToolResponse(
        filePath,
        file,
        originalContent,
        newlineChar,
        currentContent, // final content
        context,
        "str_replace",
        Array.from(results.values()),
    );
}

/**
 * Sorts replacement requests by their starting line number in descending order.
 * Processing from the bottom up prevents edits from shifting the line numbers of pending replacements.
 * @param {Array<object>} replacements - The array of replacement objects.
 * @returns {Array<object>} The sorted array of replacements.
 */
function sortReplacementsByLineNumber(replacements) {
    // Assuming `preprocessReplacements` is a function that validates or clones the replacements.
    const processedReplacements = preprocessReplacements(replacements);
    processedReplacements.sort((replacementA, replacementB) => {
        const startLineA = replacementA.old_str_start_line_number ?? -1;
        const startLineB = replacementB.old_str_start_line_number ?? -1;
        return startLineB - startLineA; // Sort in descending order
    });
    return processedReplacements;
}

/**
 * Detects the newline character sequence used in a string.
 * @param {string} content - The string content to check.
 * @returns {string} `\r\n` or `\n`.
 */
function detectNewlineChar(content) {
    return content.includes('\r\n') ? '\r\n' : '\n';
}

/**
 * Normalizes all newline characters in a string to `\n`.
 * @param {string} content - The string content to normalize.
 * @returns {string} The normalized content.
 */
function normalizeNewlines(content) {
    return content.replaceAll('\r\n', '\n');
}


/**
 * Finds if a replacement's line range overlaps with any other replacement.
 * @param {object} currentReplacement - The replacement to check.
 * @param {Array<object>} allReplacements - All replacements.
 * @returns {object|undefined} The overlapping replacement object, or undefined if none is found.
 */
function findOverlappingReplacement(currentReplacement, allReplacements) {
    const startLine = currentReplacement.old_str_start_line_number;
    const endLine = currentReplacement.old_str_end_line_number;

    if (startLine === undefined || endLine === undefined) {
        return;
    }

    for (const otherReplacement of allReplacements) {
        if (currentReplacement.index === otherReplacement.index ||
            otherReplacement.old_str_start_line_number === undefined ||
            otherReplacement.old_str_end_line_number === undefined) {
            continue;
        }

        const otherRange = {
            startLine: otherReplacement.old_str_start_line_number,
            endLine: otherReplacement.old_str_end_line_number,
        };

        const isOverlapping = (startLine <= otherRange.startLine && otherRange.startLine <= endLine) ||
                              (startLine <= otherRange.endLine && otherRange.endLine <= endLine) ||
                              (otherRange.startLine <= startLine && startLine <= otherRange.endLine);

        if (isOverlapping) {
            return otherReplacement;
        }
    }
}


/**
 * Generates an error message for overlapping replacements.
 * @param {object} replacement1 - The first replacement object.
 * @param {object} replacement2 - The second (overlapping) replacement object.
 * @returns {string} A formatted error message.
 */
function generateOverlapErrorMessage(replacement1, replacement2) {
    const startLine1 = replacement1.old_str_start_line_number + 1;
    const endLine1 = replacement1.old_str_end_line_number + 1;
    const startLine2 = replacement2.old_str_start_line_number + 1;
    const endLine2 = replacement2.old_str_end_line_number + 1;

    return `old_str line numbers range overlaps with another entry.
This entry range: [${startLine1}-${endLine1}]
Overlapping entry index: ${replacement2.index}
Overlapping entry range: [${startLine2}-${endLine2}]`;
}

/**
 * Performs a single string replacement in the given content.
 * It handles various cases, including empty strings, verbatim matches, and fuzzy matching.
 * @param {string} filePath - Path of the file.
 * @param {string} content - The current content of the file.
 * @param {string} oldStr - The string to be replaced.
 * @param {string} newStr - The string to replace with.
 * @param {number} index - The index of the replacement operation.
 * @param {number} startLine - The expected start line of oldStr.
 * @param {number} endLine - The expected end line of oldStr.
 * @returns {object} A result object, either with an error or the new content.
 */
function performSingleStringReplacement(filePath, content, oldStr, newStr, index, startLine, endLine) {
    // Normalize content and replacement strings for consistent matching.
    const normalizedContent = normalizeWhitespace(content);
    let { content: normalizedOldStr } = normalizeReplacementString(oldStr);
    let { content: normalizedNewStr } = normalizeReplacementString(newStr);
    
    // Helper to create a success result object
    const createSuccessResult = (newFileContent, finalNewStr, newStrStartLine, newStrEndLine, numLinesDiff, genMessageFunc) => ({
        isError: false,
        index,
        oldStr: normalizedOldStr,
        oldStrStartLineNumber: startLine,
        oldStrEndLineNumber: endLine,
        newContent: newFileContent,
        newStr: finalNewStr,
        newStrStartLineNumber: newStrStartLine,
        newStrEndLineNumber: newStrEndLine,
        numLinesDiff,
        genMessageFunc,
    });

    // Helper to create an error result object
    const createErrorResult = (genMessageFunc) => ({
        isError: true,
        index,
        oldStr: normalizedOldStr,
        oldStrStartLineNumber: startLine,
        oldStrEndLineNumber: endLine,
        numLinesDiff: 0,
        genMessageFunc,
    });

    let newFileContent;
    let newStrStartLine = 0;
    let newStrEndLine = 0;
    let usedFuzzyMatching = false;

    if (normalizedOldStr.trim() === "") {
        if (content.trim() === "") {
            // If oldStr and the file are empty, replace the entire file content.
            newFileContent = normalizedNewStr;
            newStrStartLine = 0;
            newStrEndLine = normalizedNewStr.split('\n').length - 1;
        } else {
            return createErrorResult(() => `No replacement was performed, old_str is empty which is only allowed when the file is empty or contains only whitespace. The file ${filePath} is not empty.`);
        }
    } else {
        // Find all verbatim matches of oldStr in the content.
        let matches = findVerbatimMatches(normalizedContent, normalizedOldStr);

        // If no verbatim match, try fixing tab indentation differences.
        if (matches.length === 0) {
            this._logger.debug("No verbatim match found for old_str. Trying tab indentation fix...");
            const tabFixResult = this.tryTabIndentFix(normalizedContent, normalizedOldStr, normalizedNewStr);
            matches = tabFixResult.matches;
            normalizedOldStr = tabFixResult.oldStr;
            normalizedNewStr = tabFixResult.newStr;
        }

        // If still no match, try fuzzy matching.
        if (matches.length === 0) {
            this._logger.debug("No verbatim match found for old_str. Trying fuzzy matching...");
            const fuzzyMatchResult = this.tryFuzzyMatching(normalizedContent, normalizedOldStr, normalizedNewStr, startLine, endLine);
            matches = fuzzyMatchResult.matches;
            normalizedOldStr = fuzzyMatchResult.oldStr;
            normalizedNewStr = fuzzyMatchResult.newStr;
            usedFuzzyMatching = matches.length > 0;
        }

        if (matches.length === 0) {
            return createErrorResult((replacement) => {
                let errorMessage = `No replacement was performed, oldStr did not appear verbatim in ${filePath}.`;
                // If line numbers were provided, add a diff for context.
                if (replacement.oldStrStartLineNumber !== undefined && replacement.oldStrEndLineNumber !== undefined) {
                    const regionContentWithLineNumbers = formatCodeSnippet(
                        content,
                        replacement.oldStrStartLineNumber,
                        replacement.oldStrEndLineNumber - replacement.oldStrStartLineNumber + 1,
                        SOME_FORMATTING_CONSTANT, // Assuming KK is a constant
                    );
                    const regionContent = content.split('\n').slice(replacement.oldStrStartLineNumber, replacement.oldStrEndLineNumber + 1).join('\n');
                    
                    const diff = generateDiff(
                        "oldStr", "regionContent",
                        replacement.oldStr + '\n',
                        regionContent + '\n',
                        undefined, undefined, { context: 3 }
                    );

                    errorMessage += `
The content in the specified region is:
${regionContentWithLineNumbers}

Diff between oldStr and the specified region is:
${diff}`;
                }
                return errorMessage;
            });
        }
        
        let matchIndex = -1;
        if (matches.length === 1) {
            matchIndex = 0;
        } else {
            // If multiple matches are found, use line numbers to disambiguate.
            if (startLine === undefined || endLine === undefined) {
                return createErrorResult(() => `Multiple occurrences of oldStr \`${normalizedOldStr}\` found. Please provide line numbers to disambiguate.`);
            }
            matchIndex = findBestMatchByLineNumbers(matches, startLine, endLine, this._lineNumberErrorTolerance);
        }

        if (matchIndex === -1) {
            return createErrorResult(() => `No match found close to the provided line numbers (${startLine + 1}, ${endLine + 1}).`);
        }

        const bestMatch = matches[matchIndex];
        const originalLines = content.split('\n');
        const normalizedLines = normalizedContent.split('\n');

        const contentBeforeMatch = originalLines.slice(0, bestMatch.startLine).join('\n');
        const contentAfterMatch = originalLines.slice(bestMatch.endLine + 1).join('\n');
        
        const matchedContent = normalizedLines.slice(bestMatch.startLine, bestMatch.endLine + 1).join('\n');
        const oldStrPosition = matchedContent.indexOf(normalizedOldStr);

        if (oldStrPosition === -1) {
            return createErrorResult(() => "Internal error: Could not find the exact position of the match.");
        }

        const textBeforeOldStr = matchedContent.substring(0, oldStrPosition);
        const textAfterOldStr = matchedContent.substring(oldStrPosition + normalizedOldStr.length);

        newFileContent = 
            contentBeforeMatch +
            (contentBeforeMatch.length > 0 ? '\n' : '') +
            textBeforeOldStr +
            normalizedNewStr +
            textAfterOldStr +
            (contentAfterMatch.length > 0 ? '\n' : '') +
            contentAfterMatch;

        newStrStartLine = bestMatch.startLine;
        newStrEndLine = bestMatch.startLine + normalizedNewStr.split('\n').length - 1;
    }

    const oldStrNumLines = normalizedOldStr.split('\n').length;
    const newStrNumLines = normalizedNewStr.split('\n').length;
    const numLinesDiff = newStrNumLines - oldStrNumLines;

    return createSuccessResult(
        newFileContent,
        normalizedNewStr,
        newStrStartLine,
        newStrEndLine,
        numLinesDiff,
        (result) => this.generateSuccessMessage(result, usedFuzzyMatching)
    );
}

/**
 * Generates a success message for a replacement operation.
 * @param {object} result - The result object of the replacement.
 * @param {boolean} wasFuzzyMatched - Whether fuzzy matching was used.
 * @returns {string} The success message.
 */
function generateSuccessMessage(result, wasFuzzyMatched) {
    const flags = getAgentFlags(); // Assuming Ei() gets some configuration flags.
    let message = "Replacement successful.";

    if (wasFuzzyMatched) {
        message = flags.agentEditToolFuzzyMatchSuccessMessage ?? message;
    }

    // Show the edited snippet if the IDE reformatted it or if the flag is set.
    if (result.wasReformattedByIDE || flags.agentEditToolShowResultSnippet) {
        const snippet = formatCodeSnippet(
            result.newContent,
            result.newStrStartLineNumber,
            result.newStrEndLineNumber - result.newStrStartLineNumber + 1,
            SOME_FORMATTING_CONSTANT, // Assuming KK is a constant
        );
        return `${message}\nEdited section after IDE auto-formatting was applied:\n${snippet}`;
    } else {
        return `${message}\nnew_str starts at line ${result.newStrStartLineNumber + 1} and ends at line ${result.newStrEndLineNumber + 1}.`;
    }
}


/**
 * Performs a replacement where oldStr and newStr are JSON objects.
 * It stringifies them and tries to find a match, accounting for different indentation styles.
 * @param {string} filePath - Path of the file.
 * @param {string} content - The current content of the file.
 * @param {object} oldJson - The JSON object to be replaced.
 * @param {object} newJson - The JSON object to replace with.
 * @param {number} index - The index of the replacement operation.
 * @param {number} startLine - The expected start line of oldJson.
 * @param {number} endLine - The expected end line of oldJson.
 * @returns {object} A result object from performSingleStringReplacement.
 */
function performSingleJsonReplacement(filePath, content, oldJson, newJson, index, startLine, endLine) {
    if (typeof oldJson !== 'object' || typeof newJson !== 'object') {
        return createSimpleErrorResult(index, String(oldJson), startLine, endLine);
    }
    
    // Stringify the old JSON object for searching.
    const oldStr = JSON.stringify(oldJson, null, 2);
    const normalizedContent = normalizeJsonWhitespace(content);
    const normalizedOldStr = normalizeJsonWhitespace(oldStr);
    
    const matches = findVerbatimMatches(normalizedContent, normalizedOldStr);

    if (matches.length === 0) {
        return createSimpleErrorResult(index, oldStr, startLine, endLine);
    }

    const contentLines = content.split('\n');
    const firstLineOfOldStr = oldStr.split('\n')[0];

    // Iterate through matches to find one that can be replaced successfully.
    for (const match of matches) {
        const leadingWhitespace = contentLines[match.startLine].match(/^([ \t]*)/)?.[1] ?? "";
        const indentInfo = detectIndent(content);
        const indentString = indentInfo.type === "tab" ? "\t" : " ".repeat(indentInfo.size);
        const isOnOwnLine = contentLines[match.startLine].indexOf(firstLineOfOldStr) === 0;

        // Re-stringify both JSON objects with the detected indentation of the match.
        const potentialOldStr = stringifyJsonWithIndent(oldJson, leadingWhitespace, indentString, isOnOwnLine);
        const potentialNewStr = stringifyJsonWithIndent(newJson, leadingWhitespace, indentString, isOnOwnLine);
        
        // Attempt the replacement with the correctly indented strings.
        const result = this.performSingleStringReplacement(filePath, content, potentialOldStr, potentialNewStr, index, startLine, endLine);
        
        if (!result.isError) {
            return result;
        }
    }
    
    // If no attempt was successful, return an error.
    return createSimpleErrorResult(index, oldStr, startLine, endLine);
}

/**
 * 验证并预处理 'str_replace' 命令的替换条目数组。
 *
 * 这个函数确保 `replacements` 参数是一个非空的对象数组，
 * 并且每个对象都包含有效的 `old_str`、`new_str` 和可选的行号。
 * 它还会为每个条目分配一个唯一的索引，并规范化行号。
 *
 * @param {Array<object>} replacements - 从命令接收到的替换条目数组。
 * @returns {Array<object>} - 一个经过验证和处理的、可以安全使用的替换条目数组。
 * @throws {Error} 如果输入不符合要求，将抛出带有清晰错误信息的异常。
 */
function validateAndPreprocessReplacements(replacements) {
  // 检查1：确保 `replacements` 参数存在
  if (replacements === undefined) {
    throw new Error(
      "缺少必要的参数 `str_replace_entries`，用于 `str_replace` 命令。",
    );
  }

  // 检查2：确保它是一个数组
  if (!Array.isArray(replacements)) {
    throw new Error(
      "无效的参数 `str_replace_entries`，用于 `str_replace` 命令。它必须是一个对象数组。",
    );
  }

  // 检查3：确保数组不为空
  if (replacements.length === 0) {
    throw new Error(
      "必要的参数 `str_replace_entries` 不能为空，用于 `str_replace` 命令。",
    );
  }

  const processedReplacements = [];
  for (const [index, replacement] of replacements.entries()) {
    // 检查4：确保数组中的每个元素都是一个对象
    if (typeof replacement !== "object" || !replacement) {
      throw new Error(
        "无效的参数 `str_replace_entries`，用于 `str_replace` 命令。它必须是一个对象数组。",
      );
    }

    // 检查5：验证 `old_str`
    if (replacement.old_str === undefined) {
      throw new Error("缺少必要的参数 `old_str`，用于 `str_replace` 命令。");
    }
    if (typeof replacement.old_str !== "string" && typeof replacement.old_str !== "object") {
      throw new Error("无效的参数 `old_str`，用于 `str_replace` 命令。它必须是一个字符串或对象。");
    }

    // 检查6：验证 `new_str`
    if (replacement.new_str === undefined) {
      throw new Error("缺少必要的参数 `new_str`，用于 `str_replace` 命令。");
    }
    if (typeof replacement.new_str !== "string" && typeof replacement.new_str !== "object") {
      throw new Error("无效的参数 `new_str`，用于 `str_replace` 命令。它必须是一个字符串或对象。");
    }

    // 检查7：验证并规范化行号
    // 0 被视为无效值，转换为 undefined
    if (replacement.old_str_start_line_number === 0) {
      replacement.old_str_start_line_number = undefined;
    }
    if (replacement.old_str_end_line_number === 0) {
      replacement.old_str_end_line_number = undefined;
    }
    
    // 如果提供了行号，必须是正整数
    if (
      replacement.old_str_start_line_number !== undefined &&
      (!Number.isInteger(replacement.old_str_start_line_number) || replacement.old_str_start_line_number < 1)
    ) {
      throw new Error("无效的参数 `old_str_start_line_number`。它必须是一个正整数。");
    }
    if (
      replacement.old_str_end_line_number !== undefined &&
      (!Number.isInteger(replacement.old_str_end_line_number) || replacement.old_str_end_line_number < 1)
    ) {
      throw new Error("无效的参数 `old_str_end_line_number`。它必须是一个正整数。");
    }

    // 将验证通过的条目添加到一个新的、干净的数组中
    processedReplacements.push({
      index: replacement.index ?? index, // 如果原始条目没有索引，就用它在数组中的位置作为索引
      old_str: replacement.old_str,
      new_str: replacement.new_str,
      old_str_start_line_number: replacement.old_str_start_line_number,
      old_str_end_line_number: replacement.old_str_end_line_number,
    });
  }

  return processedReplacements;
}
