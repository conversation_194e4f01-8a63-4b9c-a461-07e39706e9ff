const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const ffmpeg = require('fluent-ffmpeg');
const ffprobeStatic = require('ffprobe-static');

// 设置 ffprobe 路径
ffmpeg.setFfprobePath(ffprobeStatic.path);

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' })); // 只用于非视频数据的JSON请求

// 创建uploads目录
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

// 存储活跃的录制会话
const activeSessions = new Map();

// 获取视频文件的真实时长
function getVideoDuration(filePath) {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(filePath, (err, metadata) => {
            if (err) {
                console.error('获取视频元数据失败:', err);
                reject(err);
                return;
            }

            try {
                const duration = metadata.format.duration;
                if (duration && !isNaN(duration)) {
                    resolve(Math.round(duration));
                } else {
                    reject(new Error('无法获取视频时长'));
                }
            } catch (error) {
                reject(error);
            }
        });
    });
}

// 格式化时长显示
function formatDuration(seconds) {
    if (!seconds || isNaN(seconds)) return '未知';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 获取录制文件列表
app.get('/api/recordings', async (req, res) => {
    try {
        const files = fs.readdirSync(uploadsDir);
        const videoFiles = files.filter(file => file.endsWith('.mov') || file.endsWith('.mp4'));

        const fileList = await Promise.all(videoFiles.map(async (filename) => {
            const filepath = path.join(uploadsDir, filename);
            const stats = fs.statSync(filepath);

            try {
                const duration = await getVideoDuration(filepath);
                return {
                    filename,
                    size: `${(stats.size / 1024 / 1024).toFixed(2)}MB`,
                    duration: formatDuration(duration),
                    durationSeconds: duration,
                    createdAt: stats.birthtime,
                    modifiedAt: stats.mtime
                };
            } catch (error) {
                return {
                    filename,
                    size: `${(stats.size / 1024 / 1024).toFixed(2)}MB`,
                    duration: '未知',
                    durationSeconds: 0,
                    createdAt: stats.birthtime,
                    modifiedAt: stats.mtime
                };
            }
        }));

        // 按创建时间倒序排列
        fileList.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        res.json({
            success: true,
            count: fileList.length,
            files: fileList
        });
    } catch (error) {
        console.error('获取文件列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取文件列表失败',
            error: error.message
        });
    }
});

// 开始录制会话
app.post('/api/recording/start', (req, res) => {
    const sessionId = uuidv4();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const h264filename = `recording_${timestamp}_${sessionId}.h264`;
    const finalFilename = `recording_${timestamp}_${sessionId}.mov`;
    const h264filepath = path.join(uploadsDir, h264filename);
    const finalFilepath = path.join(uploadsDir, finalFilename);

    // 创建写入流，先保存为H.264原始流
    const writeStream = fs.createWriteStream(h264filepath);

    activeSessions.set(sessionId, {
        h264filename,
        finalFilename,
        h264filepath,
        finalFilepath,
        writeStream,
        startTime: new Date(),
        chunks: 0,
        totalBytes: 0
    });

    console.log(`开始录制会话: ${sessionId}, 文件: ${finalFilename}`);

    res.json({
        success: true,
        sessionId,
        filename: finalFilename,
        message: '录制会话已开始'
    });
});

// 接收视频数据块 - 使用流式处理，支持任意大小
app.post('/api/recording/chunk/:sessionId', (req, res) => {
    const { sessionId } = req.params;
    const session = activeSessions.get(sessionId);

    if (!session) {
        return res.status(404).json({
            success: false,
            message: '录制会话不存在'
        });
    }

    let chunkBytes = 0;

    // 直接将请求流管道到文件写入流，支持任意大小
    req.on('data', (chunk) => {
        chunkBytes += chunk.length;
        session.totalBytes += chunk.length;
        session.writeStream.write(chunk);
    });

    req.on('end', () => {
        session.chunks++;
        console.log(`会话 ${sessionId} 接收到数据块 #${session.chunks}, 大小: ${chunkBytes} bytes, 总计: ${(session.totalBytes / 1024 / 1024).toFixed(2)}MB`);

        res.json({
            success: true,
            message: '数据块已保存',
            chunkNumber: session.chunks,
            bytesReceived: chunkBytes,
            totalBytes: session.totalBytes
        });
    });

    req.on('error', (error) => {
        console.error(`接收数据块时出错 (会话 ${sessionId}):`, error);
        res.status(500).json({
            success: false,
            message: '接收数据块失败',
            error: error.message
        });
    });
});

// 结束录制会话
app.post('/api/recording/stop/:sessionId', async (req, res) => {
    const { sessionId } = req.params;
    const { videoDuration } = req.body; // 从客户端接收视频时长（秒）
    const session = activeSessions.get(sessionId);

    if (!session) {
        return res.status(404).json({
            success: false,
            message: '录制会话不存在'
        });
    }

    try {
        // 关闭写入流
        session.writeStream.end();

        const endTime = new Date();
        const recordingDuration = Math.round((endTime - session.startTime) / 1000);

        // 使用客户端传递的视频时长，如果没有则使用录制持续时间
        const actualVideoDuration = videoDuration || recordingDuration;
        const formattedDuration = formatDuration(actualVideoDuration);

        console.log(`录制会话结束: ${sessionId}`);
        console.log(`- 文件: ${session.finalFilename}`);
        console.log(`- 录制持续时间: ${recordingDuration}秒`);
        console.log(`- 视频实际时长: ${actualVideoDuration}秒 (${formattedDuration})`);
        console.log(`- 数据块数量: ${session.chunks}`);
        console.log(`- 总大小: ${(session.totalBytes / 1024 / 1024).toFixed(2)}MB`);

        // 使用ffmpeg将H.264原始流转换为MOV格式
        console.log(`开始转换 ${session.h264filename} 为 ${session.finalFilename}`);

        const conversionPromise = new Promise((resolve, reject) => {
            console.log(`🚀 开始快速高质量转换 - 3456x2234 Retina分辨率`);

            ffmpeg(session.h264filepath)
                .inputFormat('h264')
                .inputFPS(60) // 明确指定输入帧率为60fps
                .videoCodec('libx264') // 使用libx264编码器
                .videoBitrate('25000k') // 设置视频比特率为25Mbps（平衡质量与大小）
                .outputOptions([
                    // 🚀 快速高质量编码参数
                    '-preset', 'fast', // 使用快速预设，大幅减少转换时间
                    '-crf', '18', // 设置CRF值为18（优秀质量，减少处理时间）
                    '-profile:v', 'high', // 使用H.264 High Profile
                    '-level', '5.1', // 支持高分辨率的Level
                    '-pix_fmt', 'yuv420p', // 设置像素格式

                    // 🎯 性能优化设置
                    '-threads', '0', // 使用所有可用CPU线程
                    '-preset:v', 'fast', // 视频编码快速预设
                    '-tune', 'film', // 针对电影内容优化

                    // 🔧 简化的GOP设置
                    '-g', '60', // GOP大小为60帧（1秒）
                    '-keyint_min', '30', // 最小关键帧间隔

                    // 📱 基础播放兼容性
                    '-movflags', '+faststart', // 优化web播放
                    '-avoid_negative_ts', 'make_zero', // 避免负时间戳
                    '-fflags', '+genpts' // 生成PTS
                ])
                .outputFPS(60) // 明确指定输出帧率为60fps
                .output(session.finalFilepath)
                .on('start', (commandLine) => {
                    console.log(`🚀 FFmpeg命令: ${commandLine}`);
                })
                .on('progress', (progress) => {
                    if (progress.percent) {
                        console.log(`📊 转换进度: ${Math.round(progress.percent)}%`);
                    }
                })
                .on('end', () => {
                    console.log(`✅ 快速高质量转换完成: ${session.finalFilename}`);
                    console.log(`📊 最终文件大小: ${(fs.statSync(session.finalFilepath).size / 1024 / 1024).toFixed(2)}MB`);

                    fs.unlink(session.h264filepath, (err) => {
                        if (err) {
                            console.error(`删除临时文件失败: ${err}`);
                        } else {
                            console.log(`🗑️ 临时文件已删除: ${session.h264filename}`);
                        }
                    });

                    resolve();
                })
                .on('error', (error) => {
                    console.error(`❌ 快速高质量转换失败 ${session.finalFilename}:`, error);
                    reject(error);
                })
                .run();
        });

        // 等待转换完成
        await conversionPromise;

        // 保存视频时长信息到会话中，用于后续的文件列表查询
        session.videoDuration = actualVideoDuration;
        session.formattedDuration = formattedDuration;

        const sessionInfo = {
            sessionId,
            filename: session.finalFilename,
            recordingDuration: `${recordingDuration}秒`,
            videoDuration: `${actualVideoDuration}秒`,
            formattedDuration: formattedDuration,
            chunks: session.chunks,
            fileSize: `${(session.totalBytes / 1024 / 1024).toFixed(2)}MB`,
            startTime: session.startTime,
            endTime
        };

        // 从活跃会话中移除
        activeSessions.delete(sessionId);

        res.json({
            success: true,
            message: '录制已完成',
            ...sessionInfo
        });
    } catch (error) {
        console.error(`结束录制会话时出错 (会话 ${sessionId}):`, error);
        res.status(500).json({
            success: false,
            message: '结束录制失败',
            error: error.message
        });
    }
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('服务器错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: error.message
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');

    // 关闭所有活跃的写入流
    for (const [sessionId, session] of activeSessions) {
        console.log(`关闭会话: ${sessionId}`);
        session.writeStream.end();
    }

    process.exit(0);
});

app.listen(PORT, () => {
    console.log(`🚀 屏幕录制服务器已启动`);
    console.log(`📡 服务地址: http://localhost:${PORT}`);
    console.log(`📁 文件保存目录: ${uploadsDir}`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
    console.log('\n✨ 特性:');
    console.log('  • 支持任意大小的视频文件（无大小限制）');
    console.log('  • 流式处理，内存占用低');
    console.log('  • 多会话并发录制');
    console.log('  • 实时进度监控');
    console.log('\n📋 可用的API端点:');
    console.log('  POST /api/recording/start - 开始录制');
    console.log('  POST /api/recording/chunk/:sessionId - 上传视频数据（流式）');
    console.log('  POST /api/recording/stop/:sessionId - 停止录制');
    console.log('  GET  /api/recordings - 获取录制文件列表1');
    console.log('  GET  /health - 健康检查');
});