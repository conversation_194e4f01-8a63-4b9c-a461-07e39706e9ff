<!-- 南北分仓要改 -->
<template>
    <mc-select
        v-if="!showText"
        v-model="innerValue"
        :source="list"
        :multiple="multiple"
        value-key="poiId"
        label-key="poiName"
        v-bind="$attrs"
        placeholder="请选择或输入搜索"
        @change="onChange"
    >
        <template slot-scope="scope">
            <mc-tag
                v-if="showRegionType"
                theme=""
            >{{ scope.option.rdcRegionType }}</mc-tag>
            {{ scope.option.label }}
        </template>
    </mc-select>
    <span v-else>{{ listText }}</span>
</template>
<script>
export default {
    // 通过经营城市获取RDC
    name: 'NewRdcSelect',
    props: {
        value: {
            type: [Array, Number, String],
            default: () => [],
            required: '',
        },
        cityIds: {
            type: Array,
            default: () => [],
            required: true,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        showText: {
            type: <PERSON><PERSON>an,
            default: false,
        },
        // 数据权限
        usePermission: {
            type: Boolean,
            default: false,
        },
        showRegionType: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            list: [],
            innerValue: this.value,
        };
    },
    computed: {
        // innerValue: {
        //     get() { return Array.isArray(this.value) ? this.value : [this.value]; },
        //     set(val) { this.$emit('input', val); },
        // },
        listText() {
            const listMap = this.list.reduce((result, item) => {
                result[item.poiId] = item.poiName;
                return result;
            }, {});
            if (this.multiple) {
                return this.innerValue.map((val) => listMap[val]).join(', ');
            } else {
                return listMap[+this.innerValue];
            }
        },
    },
    watch: {
        value(newVal) {
            this.innerValue = newVal;
        },
        cityIds(newVal, oldVal) {
            if (!window._.isEqual(newVal, oldVal)) {
                this.getList();
            }
        },
    },
    created() {
        this.getList();
    },
    methods: {
        async getList() {
            try {
                const list = await this.$hPost('/api/poi/city/aggregation/managementCity/poi/rdc/mapping', {
                    managementCityIdList: this.cityIds,
                    includeTestPoi: true,
                    usePermission: this.usePermission,
                });
                // 调整返回数据顺序
                const priorityMap = {
                    默认: 0, // 默认优先级最高
                    测试: 1,
                    停用: 2,
                    废弃: 3,
                });
            } catch (error) {
                this.$message.error(error.msg || error.message || '获取仓列表失败');
            }
        },
        onChange(val, item) {
            this.$emit('input', val);
            this.$emit('change', val, item || { poiId: '', poiName: '' });
        },
    },
};
</script>