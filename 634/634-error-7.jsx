const TeacherDashboard = ({ user }) => {
    const [courses, setCourses] = React.useState([]);
    const [selectedCourse, setSelectedCourse] = React.useState(null);
    const [scores, setScores] = React.useState([]);
    const [newScore, setNewScore] = React.useState({ studentId: '', score: '' });

    React.useEffect(() => {
        fetchCourses();
    }, []);

    const fetchCourses = async () => {
        try {
            const response = await fetch(`/api/courses/teacher/${user.id}`);
            if (response.ok) {
                const data = await response.json();
                setCourses(data);
            }
        } catch (error) {
            console.error('Error:', error);
        }
    };

    const fetchScores = async (courseId) => {
        try {
            const response = await fetch(`/api/scores/course/${courseId}`);
            if (response.ok) {
                const data = await response.json();
                setScores(data);
            }
        } catch (error) {
            console.error('Error:', error);
        }
    };

    const handleCourseSelect = (courseId) => {
        setSelectedCourse(courseId);
        fetchScores(courseId);
    };

    const handleScoreSubmit = async (e) => {
        e.preventDefault();
        try {
            const response = await fetch('/api/scores', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...newScore,
                    courseId: selectedCourse,
                }),
            });
            if (response.ok) {
                fetchScores(selectedCourse);
                setNewScore({ studentId: '', score: '' });
            }
        } catch (error) {
            console.error('Error:', error);
        }
    };

    return (
        <div>
            <h1 className="text-2xl font-semibold mb-4">教师仪表板</h1>
            <div className="mb-4">
                <select
                    className="form-select"
                    onChange={(e) => handleCourseSelect(e.target.value)}
                >
                    <option value="">选择课程</option>
                    {courses.map((course) => (
                        <option key={course.id} value={course.id}>
                            {course.name}
                        </option>
                    ))}
                </select>
            </div>
            {selectedCourse && (
                <div>
                    <h2 className="text-xl font-semibold mb-2">录入成绩</h2>
                    <form onSubmit={handleScoreSubmit} className="mb-4">
                        <input
                            type="text"
                            placeholder="学生ID"
                            value={newScore.studentId}
                            onChange={(e) => setNewScore({ ...newScore, studentId: e.target.value })}
                            className="form-input mr-2"
                        />
                        <input
                            type="number"
                            placeholder="成绩"
                            value={newScore.score}
                            onChange={(e) => setNewScore({ ...newScore, score: e.target.value })}
                            className="form-input mr-2"
                        />
                        <button type="submit" className="btn btn-primary">
                            提交
                        </button>
                    </form>
                    <h2 className="text-xl font-semibold mb-2">成绩列表</h2>
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    学生ID
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    成绩
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {scores.map((score) => (
                                <tr key={score.id}>
                                    <td className="px-6 py-4 whitespace-nowrap">{score.studentId}</td>
                                    <td className="px-6 py-4 whitespace-nowrap">{score.score}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};