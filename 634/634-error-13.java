package com.sankuai.health.medicare.center.domain.support.ability;

import com.sankuai.health.medicare.center.constant.Gender;
import com.sankuai.health.medicare.center.constant.SuperviseControlScene;
import com.sankuai.shangou.yunying.utils.lion.SgYunyingConfigCenter;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class SuperviseControlAbilityTest {

    @InjectMocks
    private SuperviseControlAbility superviseControlAbility;

    private final String configJson = "[{\"cityId\":1,\"ruleList\":[{\"upc\":\"adultMaleUpc\",\"indicatedPopulation\":9},{\"upc\":\"adultUpc\",\"indicatedPopulation\":7},{\"upc\":\"childUpc\",\"indicatedPopulation\":8}]}]";


    /**
     * 测试配置为空时返回PASS
     */
    @Test
    public void testCheckSgIndicatedPopulation_ConfigEmpty() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(()->SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenReturn("");

            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 1, "testUpc", Gender.MALE, 30);

            assertEquals(SuperviseControlScene.PASS, result);
        }
    }

    /**
     * 测试找不到城市配置时返回PASS
     */
    @Test
    public void testCheckSgIndicatedPopulation_CityConfigNotFound() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(() -> SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenReturn(configJson);

            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 2, "testUpc", Gender.MALE, 30);

            assertEquals(SuperviseControlScene.PASS, result);
        }
    }

    /**
     * 测试找不到UPC配置时返回PASS
     */
    @Test
    public void testCheckSgIndicatedPopulation_UpcNotFound() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(() -> SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenReturn(configJson);
            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 1, "wrongUpc", Gender.MALE, 30);

            assertEquals(SuperviseControlScene.PASS, result);
        }
    }

    /**
     * 测试性别和年龄都符合时返回PASS
     */
    @Test
    public void testCheckSgIndicatedPopulation_AgeAndGenderMatch() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(() -> SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenReturn(configJson);
            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 1, "adultMaleUpc", Gender.MALE, 30);

            assertEquals(SuperviseControlScene.PASS, result);
        }
    }

    /**
     * 测试性别不符合时返回GENDER_UNMATCH
     */
    @Test
    public void testCheckSgIndicatedPopulation_GenderUnmatch() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(() -> SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenReturn(configJson);
            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 1, "adultMaleUpc", Gender.FEMALE, 30);

            assertEquals(SuperviseControlScene.GENDER_UNMATCH, result);
        }
    }

    /**
     * 测试年龄不符合时返回AGE_UNMATCH
     */
    @Test
    public void testCheckSgIndicatedPopulation_AgeUnmatch() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(() -> SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenReturn(configJson);
            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 1, "childUpc", Gender.MALE, 18);

            assertEquals(SuperviseControlScene.AGE_UNMATCH, result);
        }
    }

    /**
     * 测试异常情况时返回PASS
     */
    @Test
    public void testCheckSgIndicatedPopulation_Exception() {
        try (MockedStatic<SgYunyingConfigCenter> mockedStatic = mockStatic(SgYunyingConfigCenter.class)) {
            mockedStatic.when(() -> SgYunyingConfigCenter.getJsonValueData(SuperviseControlAbility.MEDICARE_INSURANCE_DRUG_SUPERVISE_RULE))
                .thenThrow(new RuntimeException());

            SuperviseControlScene result = ReflectionTestUtils.invokeMethod(superviseControlAbility, "checkSgIndicatedPopulation", 1, "testUpc", Gender.MALE, 30);

            assertEquals(SuperviseControlScene.PASS, result);
        }
    }
}