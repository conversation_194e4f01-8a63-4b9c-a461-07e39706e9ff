from common.msi_concurrent_test import MSIConcurrentTest
from common.common_assert_msi import *


@MSIConcurrentTest(name="test_data_showToast")
class TestStartLocationUpdate(commonAssertMsi):

    def msi_data_p0_mt_token_correct(self):
        """
        测试场景：_mt参数，类型正确，符合权限token
        预期结果：success回调
        """
        data = {
            "_mt": {"sceneToken": self.TOKEN_START_LOCATION_UPDATE}
        }
        result = self.call_wx_method("startLocationUpdate", data)
        self.assert_success_code(result, msg="测试_mt参数，类型正确，符合权限token，预期success回调")

    def msi_data_p1_mt_token_empty(self):
        """
        测试场景：_mt参数，类型正确，空字符串
        预期结果：fail回调
        """
        data = {
            "_mt": {"sceneToken": ""}
        }
        result = self.call_wx_method("startLocationUpdate", data)
        expect_codes = {"ios": 400, "android": 410, "harmony": 500}
        self.assert_fail_code(result, msg="测试_mt参数，类型正确，空字符串，预期fail回调",
                              expect_code=expect_codes[self.platform],
                              expect_error_no=29999)

    def msi_data_p1_mt_token_invalid(self):
        """
        测试场景：_mt参数，类型正确，不符合权限token
        预期结果：fail回调
        """
        data = {
            "_mt": {"sceneToken": "invalid_token"}
        }
        result = self.call_wx_method("startLocationUpdate", data)
        expect_codes = {"ios": 400, "android": 410, "harmony": 500}
        self.assert_fail_code(result, msg="测试_mt参数，类型正确，不符合权限token，预期fail回调",
                              expect_code=expect_codes[self.platform],
                              expect_error_no=29999)

    def msi_data_p1_mt_token_random(self):
        """
        测试场景：_mt参数，类型正确，随机字符串
        预期结果：fail回调
        """
        data = {
            "_mt": {"sceneToken": "random_string"}
        }
        result = self.call_wx_method("startLocationUpdate", data)
        expect_codes = {"ios": 400, "android": 410, "harmony": 500}
        self.assert_fail_code(result, msg="测试_mt参数，类型正确，随机字符串，预期fail回调",
                              expect_code=expect_codes[self.platform],
                              expect_error_no=29999)

    def msi_data_p1_mt_token_type_error(self):
        """
        测试场景：_mt参数，类型错误，输入非string类型变量
        预期结果：fail回调并提示request._mt except object but get number
        """
        data = {
            "_mt": {"sceneToken": 12345}
        }
        result = self.call_wx_method("startLocationUpdate", data)
        expect_codes = {"ios": 400, "android": 410, "harmony": 500}
        self.assert_fail_code(result, msg="测试_mt参数，类型错误，输入非string类型变量，预期fail回调",
                              expect_code=expect_codes[self.platform],
                              expect_error_no=29999)

    def msi_data_p1_mt_token_null(self):
        """
        测试场景：_mt参数，输入null
        预期结果：fail回调并提示auth denied by remote
        """
        data = {
            "_mt": None
        }
        result = self.call_wx_method("startLocationUpdate", data)
        expect_codes = {"ios": 400, "android": 410, "harmony": 500}
        self.assert_fail_code(result, msg="测试_mt参数，输入null，预期fail回调", expect_code=expect_codes[self.platform],
                              expect_error_no=29999)

    def msi_data_p1_mt_token_missing(self):
        """
        测试场景：_mt参数，不输入
        预期结果：fail回调
        """
        data = {}
        result = self.call_wx_method("startLocationUpdate", data)
        expect_codes = {"ios": 400, "android": 410, "harmony": 500}
        self.assert_fail_code(result, msg="测试_mt参数，不输入，预期fail回调", expect_code=expect_codes[self.platform],
                              expect_error_no=29999)

    def msi_data_p0_needDetailResult_correct(self):
        """
        测试场景：needDetailResult参数，类型正确，输入正确类型
        预期结果：success回调，返回loadconfig详细配置
        """
        data = {
            "_mt": {"sceneToken": self.TOKEN_START_LOCATION_UPDATE},
            "needDetailResult": True
        }
        result = self.call_wx_method("startLocationUpdate", data)
        self.assert_success_code(result, msg="测试needDetailResult参数，类型正确，输入正确类型，预期success回调")

    def msi_data_p1_needDetailResult_type_error(self):
        """
        测试场景：needDetailResult参数，类型错误，输入非boolean类型变量
        预期结果：success回调
        """
        data = {
            "_mt": {"sceneToken": self.TOKEN_START_LOCATION_UPDATE},
            "needDetailResult": "true"
        }
        result = self.call_wx_method("startLocationUpdate", data)
        self.assert_success_code(result, msg="测试needDetailResult参数，类型错误，输入非boolean类型变量，预期success回调")

    def msi_data_p1_needDetailResult_null(self):
        """
        测试场景：needDetailResult参数，输入null
        预期结果：success回调
        """
        data = {
            "_mt": {"sceneToken": self.TOKEN_START_LOCATION_UPDATE},
            "needDetailResult": None
        }
        result = self.call_wx_method("startLocationUpdate", data)
        self.assert_success_code(result, msg="测试needDetailResult参数，输入null，预期success回调")

    def msi_data_p1_needDetailResult_missing(self):
        """
        测试场景：needDetailResult参数，不输入
        预期结果：success回调
        """
        data = {
            "_mt": {"sceneToken": self.TOKEN_START_LOCATION_UPDATE}
        }
        result = self.call_wx_method("startLocationUpdate", data)
        self.assert_success_code(result, msg="测试needDetailResult参数，不输入，预期success回调")

    def msi_data_p1_needDetailResult_harmony_skip(self):
        """
        测试场景：needDetailResult参数，鸿蒙端暂不支持
        """
        if self.platform == "harmony":
            return self.skip_test_msi()



# ... additional cases for other parameters and scenarios ...