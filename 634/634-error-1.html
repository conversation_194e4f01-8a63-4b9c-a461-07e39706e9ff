<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            transition: background-color 0.3s, color 0.3s;
            background-color: #fff;
        }
        .container {
            background-color: #fff;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 320px;
            transition: all 0.3s ease;
        }
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
            font-size: 1.8rem;
        }
        form {
            display: flex;
            flex-direction: column;
        }
        input {
            margin-bottom: 1.5rem;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        input:focus {
            outline: none;
            border-color: #666;
        }
        button {
            padding: 0.8rem;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        button:hover {
            background-color: #555;
        }
        #theme-toggle {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            background-color: transparent;
            color: #333;
            border: 2px solid #333;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            outline: none;
        }
        #theme-toggle:hover {
            background-color: #333;
            color: #fff;
        }
        #theme-toggle:active {
            transform: scale(0.95);
        }
        .dark-theme #theme-toggle {
            color: #fff;
            border-color: #fff;
        }
        .dark-theme #theme-toggle:hover {
            background-color: #fff;
            color: #333;
        }
        .dark-theme {
            background-color: #333;
            color: #fff;
        }
        .light-theme {
            background-color: #fff;
            color: #333;
        }
        .dark-theme .container {
            background-color: #444;
            box-shadow: 0 4px 6px rgba(255, 255, 255, 0.1);
        }
        .light-theme .container {
            background-color: #fff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .dark-theme input {
            background-color: #555;
            color: #fff;
            border-color: #666;
        }
        .light-theme input {
            background-color: #fff;
            color: #333;
            border-color: #ddd;
        }
        .dark-theme button {
            background-color: #fff;
            color: #333;
        }
        .dark-theme button:hover {
            background-color: #e0e0e0;
        }
        .register-link {
            text-align: center;
            margin-top: 1.5rem;
            font-size: 0.9rem;
        }
        .register-link a {
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .register-link a:hover {
            color: #333;
            text-decoration: underline;
        }
        .dark-theme .register-link a {
            color: #ccc;
        }
        .dark-theme .register-link a:hover {
            color: #fff;
        }
        /* 添加图标 */
        #theme-toggle::before {
            content: '??';
            margin-right: 5px;
        }
    </style>
</head>
<body class="light-theme">
    <button id="theme-toggle">切换主题</button>
    <div class="container">
        <h1>登录</h1>
        <form>
            <input type="text" placeholder="用户名" required>
            <input type="password" placeholder="密码" required>
            <button type="submit">登录</button>
        </form>
        <p class="register-link">
            还没有账号？<a href="register.html">立即注册</a>
        </p>
    </div>

    <script>
        const body = document.body;
        const themeToggle = document.getElementById('theme-toggle');

        themeToggle.addEventListener('click', () => {
            body.classList.toggle('dark-theme');
            body.classList.toggle('light-theme');
        });

        // 设置初始主题
        if (!body.classList.contains('dark-theme') && !body.classList.contains('light-theme')) {
            body.classList.add('light-theme');
        }
    </script>
</body>
</html>