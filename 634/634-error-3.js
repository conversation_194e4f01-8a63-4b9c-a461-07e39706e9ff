import { IS_PRO, IS_TEST } from '@/lib/env';

const isDev = !IS_PRO && !IS_TEST;

// interface WebRTCConnectionInitOptions {
//   onConnected(event?: Event): void;
//   onDisconnected?(event?: Event): void;
//   onFailed?(event?: Event): void;
//   onDatachannelOpen?(event?: Event): void;
//   onDatachannelMessage(event: MessageEvent): void;
//   onTrack(event: RTCTrackEvent): void;
//   onDatachannelError(event: Event): void;
// }

// eslint-disable-next-line import/prefer-default-export
export class WebRTCConnection {
  #pc;

  #emptyVideoTrack;

  #emptyAudioTrack;

  #datachannel;

  constructor(rtcConfig) {
    this.#pc = new RTCPeerConnection(rtcConfig);

    // 绑定空轨道
    const emptyCanvas = document.createElement('canvas');
    emptyCanvas.width = 1;
    emptyCanvas.height = 1;
    const videoEmptyStream = emptyCanvas.captureStream();
    // eslint-disable-next-line prefer-destructuring
    this.#emptyVideoTrack = videoEmptyStream.getVideoTracks()[0];
    const audioContext = new window.AudioContext();
    const destination = audioContext.createMediaStreamDestination();
    // eslint-disable-next-line prefer-destructuring
    this.#emptyAudioTrack = destination.stream.getAudioTracks()[0];
    this.#pc.addTrack(this.#emptyVideoTrack);
    this.#pc.addTrack(this.#emptyAudioTrack);

    this.#datachannel = this.#pc.createDataChannel('textChannel');

    // this.#prepareRTC();
  }

  async init(options) {
    this.#pc.onconnectionstatechange = (e) => {
      console.log('连接状态变化 当前状态：', this.#pc.connectionState);
      switch (this.#pc.connectionState) {
        case 'connected': {
          options.onConnected(e);
          break;
        }
        case 'disconnected': {
          options.onDisconnected?.(e);
          break;
        }
        case 'failed': {
          options.onFailed?.(e);
          break;
        }
        default:
          break;
      }
    };

    this.#pc.ontrack = options.onTrack;

    // this.#datachannel.onmessage = options.onDatachannelMessage;
    this.#datachannel.onmessage = (event) => {
      console.log('Received message:', event.data);
    };
    this.#datachannel.onopen = options.onDatachannelOpen ?? null;
    this.#datachannel.onerror = options.onDatachannelError;
    await this.#prepareRTC();
  }

  async #prepareRTC() {
    try {
      console.log('current pc: ', this.#pc);
      const initialOffer = await this.#pc.createOffer();
      console.log('iodsofisww');
      // 修改sdp
      initialOffer.sdp = initialOffer.sdp?.replace(
        'a=fmtp:111 minptime=10;useinbandfec=1',
        'a=fmtp:111 minptime=40;ptime=40;useinbandfec=1;maxplaybackrate=16000;stereo=0'
      );
      // setLocalDescription
      console.log('setLocalDescription');
      await this.#pc.setLocalDescription(initialOffer);
    } catch (err) {
      console.log('创建连接失败: ', err);
    }
  }

  #gatherICECandidates() {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // eslint-disable-next-line prefer-promise-reject-errors
        reject('timeout');
      }, 60000); // 1min超时时间
      const checkIceGatheringState = () => {
        console.log('icegatheringstate chagne: ', this.#pc.iceGatheringState);
        if (this.#pc.iceGatheringState === 'complete') {
          this.#pc.removeEventListener('icegatheringstatechange', checkIceGatheringState);
          resolve(0);
        }
      };
      if (this.#pc.iceGatheringState === 'complete') {
        resolve(0);
      } else {
        this.#pc.addEventListener('icegatheringstatechange', checkIceGatheringState);
      }
    });
  }

  async getRemoteSdp(params) {
    try {
      await this.#gatherICECandidates();
      const offer = this.#pc.localDescription;

      if (!offer?.sdp) {
        throw new Error('No local description available');
      }
      // const url = '/digitalhuman/init';

      let url = 'https://horus.sankuai.com/api/digitalhuman/init';

      if (isDev) {
        url = '/digitalhuman/init';
        // url = 'https://digitalhuman.vision.test.sankuai.com/init';
      }

      if (IS_TEST) {
        url = 'https://digitalhuman.vision.test.sankuai.com/init';
      }

      if (IS_PRO) {
        // 内网
        url = 'https://horus.sankuai.com/api/digitalhuman/init';
        // // 外网
        // url = 'https://aigc.meituan.com/init';
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_mode: params.chat_mode,
          sdp: offer?.sdp,
          dh_config: {
            model_name: params.model_name,
          },
          llm_config: {
            app_id: params.app_id,
            bot_id: params.bot_id,
            app_factory: params.app_factory,
            bot_timeout_reply: params.bot_timeout_reply,
            ext_info: params.ext_info,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error in getRemoteSdp:', error);
      throw error;
    }
  }

  // async connect({ mode, modelId, botId, voiceId, targetUrl }) {
  async connect(params, targetUrl) {
    // const { mode, modelId, botId, voiceId } = params;
    console.log('connect', params, targetUrl);
    try {
      await this.#gatherICECandidates();
    } catch (err) {
      throw new Error(`创建连接失败：${err?.toString()}`);
    }

    // TODO: 申请连接视频通话
    console.log('开始申请视频链接通话');
    const offer = this.#pc.localDescription;
    console.log('local offer: ', offer);
    // const dhParams = {
    //   ...params,
    //   sdp: offer?.sdp,
    // };

    // const params = {
    //   chat_mode: mode,
    //   sdp: offer?.sdp,
    //   ...(modelId && {
    //     dh_config: {
    //       model_name: modelId,
    //     },
    //   }),
    //   ...(botId && {
    //     llm_config: {
    //       bot_id: botId,
    //     },
    //   }),
    //   tts_config: {
    //     voice_name: voiceId,
    //   },
    // };

    // const res = await connectChat(dhParams, targetUrl);
    const res = await this.getRemoteSdp(params);
    console.log('getRemoteSdp res remote sdp', res);
    this.#pc.setRemoteDescription(res);
    return res;
  }

  replaceTrack(type, track) {
    if (!track) return;
    // eslint-disable-next-line default-case
    switch (type) {
      case 'audio': {
        const sender = this.#pc.getSenders().find((s) => s.track?.kind === 'audio');
        sender?.replaceTrack(track);
        break;
      }
      case 'video': {
        const sender = this.#pc.getSenders().find((s) => s.track?.kind === 'video');
        sender?.replaceTrack(track);
        break;
      }
    }
  }

  setEmptyTrack(type) {
    // eslint-disable-next-line default-case
    switch (type) {
      case 'audio': {
        const sender = this.#pc.getSenders().find((s) => s.track?.kind === 'audio');
        sender?.replaceTrack(this.#emptyAudioTrack);
        break;
      }
      case 'video': {
        const sender = this.#pc.getSenders().find((s) => s.track?.kind === 'video');
        sender?.replaceTrack(this.#emptyVideoTrack);
        break;
      }
    }
  }

  sendTextMessage(text, imgBase64 = '', imgUrl = '') {
    if (!this.#datachannel || this.#datachannel.readyState !== 'open') {
      console.error('Data channel is not open. Cannot send message.');
      return;
    }

    const message = {
      text,
      ...(imgBase64 && { img_base64: imgBase64 }),
      ...(imgUrl && { img_url: imgUrl }),
    };

    try {
      this.#datachannel.send(JSON.stringify(message));
      console.log('Text message sent:', message);
    } catch (error) {
      console.error('Failed to send text message:', error);
    }
  }

  sendMessage(message) {
    if (!this.#datachannel || this.#datachannel.readyState !== 'open') {
      return false;
    }

    console.log('datachannel send message: ', message);
    this.#datachannel.send(message);
    return true;
  }

  setMicrophoneStatus(enabled) {
    const sender = this.#pc.getSenders().find((s) => s.track?.kind === 'audio');
    if (sender?.track) {
      sender.track.enabled = enabled;
    }
  }

  close() {
    this.#datachannel?.close();
    this.#pc?.close();
  }
}