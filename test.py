def bubble_sort(arr):
    """
    冒泡排序算法

    通过重复遍历要排序的数列，一次比较两个元素，如果它们的顺序错误就把它们交换过来。
    遍历数列的工作是重复地进行直到没有再需要交换，也就是说该数列已经排序完成。

    Args:
        arr (list): 待排序的数组

    Returns:
        list: 排序后的数组

    时间复杂度: O(n²)
    空间复杂度: O(1)
    """
    n = len(arr)

    for i in range(n):
        swapped = False

        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
                swapped = True

        if not swapped:
            break

    return arr

def quick_sort(arr):
    """
    快速排序算法

    通过选择一个基准值（pivot），将数组分为两个子数组，一个子数组包含小于基准值的所有元素，
    另一个子数组包含大于基准值的所有元素。然后递归地对这两个子数组进行排序。

    Args:
        arr (list): 待排序的数组

    Returns:
        list: 排序后的数组

    时间复杂度: 平均 O(n log n)，最坏 O(n²)
    空间复杂度: O(log n)
    """
    if len(arr) <= 1:
        return arr

    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]

    return quick_sort(left) + middle + quick_sort(right)

def insertion_sort(arr):
    """
    插入排序算法

    将数组分为已排序和未排序两部分，每次从未排序部分取出一个元素，
    插入到已排序部分的合适位置。

    Args:
        arr (list): 待排序的数组

    Returns:
        list: 排序后的数组

    时间复杂度: O(n²)
    空间复杂度: O(1)
    """
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

def selection_sort(arr):
    """
    选择排序算法

    每次从未排序区间中找到最小的元素，将其放到已排序区间的末尾。

    Args:
        arr (list): 待排序的数组

    Returns:
        list: 排序后的数组

    时间复杂度: O(n²)
    空间复杂度: O(1)
    """
    for i in range(len(arr)):
        min_idx = i
        for j in range(i + 1, len(arr)):
            if arr[j] < arr[min_idx]:
                min_idx = j
        arr[i], arr[min_idx] = arr[min_idx], arr[i]
    return arr

def merge_sort(arr):
    """
    归并排序算法

    采用分治策略，将数组分成两半，分别排序后再合并。

    Args:
        arr (list): 待排序的数组

    Returns:
        list: 排序后的数组

    时间复杂度: O(n log n)
    空间复杂度: O(n)
    """
    if len(arr) <= 1:
        return arr

    mid = len(arr) // 2
    left = merge_sort(arr[:mid])
    right = merge_sort(arr[mid:])

    return merge(left, right)

def merge(left, right):
    """
    合并两个已排序的数组

    Args:
        left (list): 左半部分已排序数组
        right (list): 右半部分已排序数组

    Returns:
        list: 合并后的已排序数组
    """
    result = []
    i = j = 0

    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1

    result.extend(left[i:])
    result.extend(right[j:])
    return result
