import * as vscode from 'vscode';
import { WorkspaceFolder } from 'vscode';
import { FileUtils } from "../../../infrastructure/utils/fileUtils";
import { clearForTag, getAddRemoveForTag, getRemoveForTag, saveAddRemoveForTag } from "./refreshIndex";
import { CodebaseIndex, IndexingProgressUpdate, IndexTag, FileOperationResults, ClearIndexResult, ClearResultType, AllIndexFiles } from "./types";
import { EmbeddingCodebaseIndex } from "./EmbeddingCodebaseIndex";
import { clearInterval } from 'timers';
import { pLimit } from 'plimit-lit';
import { getExcludePattern } from '../../../common/findWorkspaceFiles';
import IndexProgress from './indexProgress';
import { indexResultsToIndexOperations, sleep } from './utils';
import { getSavedItemsForTag } from './refreshIndex';
import IndexLog from './IndexLog';
import { LIMIT_FILE_CONTENT_LENGTH, LIMIT_INDEX_WORKSPACE_FILE_COUNT } from '../../../common/consts';
import { isBinaryFile } from "isbinaryfile";
import IndexUtils from "../../../common/indexUtils";
import { isEmpty } from "lodash";
import { CatpawGlobalConfig } from '../../../common/CatpawGlobalConfig';
/**
 * 仓库 Indexer 监听器，用于触发 CodebaseIndex 进行索引更新
 *
 * 对标到 cursor 编辑器，也是 RepoIndexWatcher 名字
 * 对标到 continue 插件，是 CodebaseIndexer 名字
 */
export class RepoIndexWatcher implements vscode.Disposable {

    static instance: RepoIndexWatcher;

    /**
     * 临时改成20个，目前单个文件最大限制2M, 50个文件容易超出nginx(50M)的上限
     * TODO: 后期每个批次发出去的文章应该按照 (nginx上限50M-5M)去动态计算
     */
    filesPerBatch = 20;

    /**
     * 索引刷新间隔，主要有两个目的：
     * - 兜底，避免一些监听缺失，部分增量的代码，没有更新到
     * - 后端 embeddings 策略变更，可能需要触发全量的 reindex 重索引
     */
    refreshIntervals = 5 * 60 * 1000;
    // refreshIntervals = 15 * 1000;

    // 成功的文件占比超过这个数值就算索引成功了
    successPercent: number = 0.95;

    private refreshTimer: NodeJS.Timeout | undefined;

    /**
     * 是否开启慢模式
     */
    isSlowMode: boolean = true;

    /**
     * 锁：避免并发执行更新索引
     */
    private lock = pLimit(1);

    private indexingState: IndexingProgressUpdate = {
        status: "loading",
        progress: 0,
        desc: "",
    };

    clearLock() {
        this.lock.clearQueue();
        this.lock = pLimit(1);
    }

    updateSlowMode(status: boolean = true) {
        this.isSlowMode = status;
    }

    static getInstance(subscriptions: vscode.Disposable[]) {
        if (!this.instance) {
            this.instance = new RepoIndexWatcher(subscriptions);
        }
        return this.instance;
    }

    constructor(private _subscriptions: vscode.Disposable[]) {
        // 首次初始化 + git 分支变化
        const gitExtension = vscode.extensions.getExtension('vscode.git')?.exports;
        if (gitExtension) {
            try {
                const gitApi = gitExtension.getAPI(1);
                if (gitApi.state === 'initialized') {
                    this.init(_subscriptions, gitApi);
                } else {
                    this._subscriptions.push(gitApi.onDidChangeState(async (event: string) => {
                        if (event !== 'initialized') {
                            return;
                        }
                        this.init(_subscriptions, gitApi);
                    }));
                }
            } catch (error) {
                console.error('[RepoIndexWatcher] 索引初始化失败，git 环境异常', error);
                this.init(_subscriptions, null);
            }
        } else {
            this.init(_subscriptions, null);
        }
    }

    private init(_subscriptions: vscode.Disposable[], gitApi: any) {
        if (gitApi) {
            this.initGitBranchListener(_subscriptions, gitApi);
        }
        // this.initFileListener(_subscriptions);
        this.initTimerListener(_subscriptions);

        // 首次初始化
        this.checkClearAndRefresh();
    }

    private initGitBranchListener(_subscriptions: vscode.Disposable[], gitApi: any) {
        const repository = gitApi.repositories[0];
        if (!repository) {
            return;
        }
        // git 分支变化
        let branchName = repository.state.HEAD?.name;
        this._subscriptions.push(repository.state.onDidChange(async () => {
            const newBranchName = repository.state.HEAD?.name;
            if (newBranchName === branchName) {
                return;
            }
            await this.lock(async () => {
                await this.refresh(true);
                branchName = newBranchName;
            });
        }));
    }

    // private initFileListener(_subscriptions: vscode.Disposable[]) {
    //     // 监听文件内容变化
    //     this._subscriptions.push(vscode.workspace.onDidSaveTextDocument(async (event) => {
    //         // Listen for file changes in the workspace
    //         const filepath = event.uri.fsPath;

    //         // .gitignore 发生变化，需要全索引
    //         if (filepath.endsWith(".continueignore")
    //             || filepath.endsWith(".gitignore")) {
    //             // Reindex the workspaces
    //             await this.lock(() => this.refresh(true));
    //         } else {
    //             // 单个文件发布变化，只索引该文件
    //             // Reindex the file
    //             await this.lock(() => this.refreshFile(filepath));
    //         }
    //     }));

    //     // 监听文件删除
    //     this._subscriptions.push(vscode.workspace.onDidDeleteFiles(async (event) => {
    //         await this.lock(() => this.deleteFile(event.files.map(file => file.fsPath)));
    //     }));
    // }

    private initTimerListener(_subscriptions: vscode.Disposable[]) {
        // 定时更新
        this.refreshTimer = setInterval(this.checkClearAndRefresh, this.refreshIntervals);
    }


    checkClearAndRefresh = async () => {
        await this.lock(async () => {
            // 清空索引，如果需要重索引
            const { type: clearType, message = "清除失败" }: ClearIndexResult = await this.clearIndexIfNeeded();
            switch (clearType) {
                case ClearResultType.UN_NEED_CLEAR:  // 不需要清除
                case ClearResultType.CLEARED: { // 已经完全清除
                    await this.refresh(true);
                    break;
                }
                case ClearResultType.CLEAR_ERROR: { // 清除异常
                    // 清除异常的情况下禁止进行索引，所以这里判断一下索引进度
                    // tip: check失败不代表index失败
                    // this.checkPreviousProgress();
                    IndexLog.clearError(message);
                    this.indexingState = IndexProgress.clearError(message);
                    break;
                }
                default: {
                    const message = `异常的枚举类型 ${clearType}`;
                    console.error(IndexLog.logLabel, message);
                    this.indexingState = IndexProgress.clearError(message);
                    break;
                }
            }
        });
    };

    /**
     * 计算当前仓库索引的总进度
    */
    async getWorkspaceIndexProgress(allIndexFiles: AllIndexFiles): Promise<number> {
        const workspaceDirs = this.getIndexWorkspaceDirs();
        if (!workspaceDirs || workspaceDirs.length === 0) {
            this.indexingState = IndexProgress.noWorkspace();
            return 0;
        }
        const indexesToBuild = this.getIndexesToBuild();
        const firstBuildIndex = indexesToBuild[0];

        const tags: IndexTag[] = IndexUtils.getIndexTags(firstBuildIndex);

        // 获取所有工作区的已索引文件
        let totalIndexedFiles = 0;
        const allIndexFilesPaths = Object.keys(allIndexFiles);
        const allFilesSet = new Set(allIndexFilesPaths);

        // 遍历所有工作区的标签
        for (const tag of tags) {
            // 获取已经索引的文件
            const savedItems = await getSavedItemsForTag(tag);
            // 只判断 数据库中 hash 相同的文件作为初始进度
            for (const item of savedItems) {
                if (allFilesSet.has(item.absolutePath)) {
                    totalIndexedFiles++;
                }
            }
        }

        // 计算索引进度
        const totalFiles = allIndexFilesPaths.length;
        let progress = totalFiles > 0 ? totalIndexedFiles / totalFiles : 0;
        progress = parseFloat(Number(progress).toFixed(2));
        return progress;
    }

    // getIndexWorkspace(): vscode.WorkspaceFolder | undefined {
    //     const { size, workspace } = IndexUtils.getIndexWorkspace();
    //     if (size === 0) {
    //         console.log('[RepoIndexWatcher][refresh][no workspace]');
    //         return;
    //     }
    //     if (size > 1) {
    //         IndexLog.multiRepo();
    //         console.log('[RepoIndexWatcher][refresh][multiple workspace，只处理第一个]');
    //     }
    //     return workspace;
    // }

    getIndexWorkspaceDirs(): vscode.WorkspaceFolder[] | undefined {
        const { size, workspaceDirs } = IndexUtils.getIndexWorkspaceDirs();
        if (size === 0) {
            console.log('[RepoIndexWatcher][refresh][no workspace]');
            return;
        }
        if (size > 1) {
            IndexLog.multiRepo();
            console.log('[RepoIndexWatcher][refresh][multiple workspace]');
        }
        return workspaceDirs;
    }

    /**
     * 获取需要索引的文件列表
     * 1. 过滤超过一定大小的文件
     * 2. 过滤掉二进制文件
     * 3. 过滤正则文件
     */
    async getAllIndexFiles(): Promise<AllIndexFiles> {
        const workspaceDirs = this.getIndexWorkspaceDirs();
        if (!workspaceDirs || workspaceDirs.length === 0) {
            this.indexingState = IndexProgress.noWorkspace();
            return {};
        }
        console.log(`[RepoIndexWatcher][getAllIndexFiles][workspaceDirs: ${IndexUtils.getIndexWorkspacePaths()}]`);

        let allFiles: AllIndexFiles = {};

        // 遍历所有工作区目录
        for (const workspaceDir of workspaceDirs) {
            const includePattern = new vscode.RelativePattern(workspaceDir, '**/*');
            const excludePattern = await getExcludePattern(workspaceDir);
            console.log('[RepoIndexWatcher][getAllIndexFiles] excludePattern', excludePattern);
            const workspaceFiles = await vscode.workspace.findFiles(includePattern, excludePattern);
            console.log(`[RepoIndexWatcher][getAllIndexFiles] workspaceFiles for ${workspaceDir.uri.fsPath}`, workspaceFiles.length);

            // 获取并过滤文件
            const indexFiles = await this.getMtimeMsAndFilterUnSupportFileBySlowMode(workspaceFiles.map(file => file.fsPath));
            
            // 检查单个工作区的文件数量是否超过阈值
            const indexFilesLength = Object.keys(indexFiles).length;
            console.log(`[RepoIndexWatcher][getAllIndexFiles] workspaceFiles after filter for ${workspaceDir.uri.fsPath}`, indexFilesLength);

        if (indexFilesLength > LIMIT_INDEX_WORKSPACE_FILE_COUNT) {
            this.indexingState = IndexProgress.fileCountsIsoverLimit(LIMIT_INDEX_WORKSPACE_FILE_COUNT);
            return {};
        }
            
            // 合并结果
            allFiles = { ...allFiles, ...indexFiles };
    }

        const totalIndexFilesLength = Object.keys(allFiles).length;
        console.log('[RepoIndexWatcher][getAllIndexFiles] total workspaceFiles after filter', totalIndexFilesLength);
        
        return allFiles;
    }
    async refresh(updateProgress: boolean = false) {
        if (CatpawGlobalConfig.isEnable('DISATBLE_CODE_BASE')) {
            return;
        }
        console.log(`[RepoIndexWatcher][refresh][updateProgress: ${updateProgress}]`);
        let progress = 0;

        if (updateProgress) {
            this.indexingState = IndexProgress.startLoading();
        }
        const beginTime = Date.now();
        const workspaceDirs = this.getIndexWorkspaceDirs();
        if (!workspaceDirs || workspaceDirs.length === 0) {
            this.indexingState = IndexProgress.responseFailure({
                message: `索引失败: 获取索引仓库异常`
            });
            return;
        }

        // 获取所有工作区的文件
        const allIndexFiles = await this.getAllIndexFiles();
        // 进行索引
        let nextLogThreshold = 0;
        try {
            for await (const updateDesc of this.indexFiles(allIndexFiles)) {
                    this.indexingState = updateDesc;
                    // 如果需要重新索引，则直接结束
                    if (updateDesc.shouldClearIndexes) {
                        // TODO 优化：未来看情况，这里可以直接发起重新索引
            this.indexingState = {
                            ...updateDesc,
                            status: "failed"
            };
                    return;
                }
                    if (updateDesc.progress >= nextLogThreshold) {
                        // log progress every 2.5%
                        nextLogThreshold += 0.025;
                        this.logProgress(
                            beginTime,
                            Math.floor(Object.keys(allIndexFiles).length * updateDesc.progress),
                            updateDesc.progress,
                        );
            }
                    console.log(`[RepoIndexWatcher][refresh][indexingState: ${JSON.stringify(this.indexingState)}]`);
    }
        } catch (err: Error | any) {
            console.error(`[RepoIndexWatcher][refresh][error: ${err.message}]`);
            this.indexingState = {
                status: "failed",
                progress: progress,
                desc: err.message,
            };
            return;
        }
        console.log('[RepoIndexWatcher][refresh][end]');
    }

    // public async refreshFile(file: string): Promise<void> {
    //     console.log(`[RepoIndexWatcher][refreshFile][file: ${file}]`); // Log file being refreshed
    //     // if (this.pauseToken.paused) {
    //     //     // NOTE: by returning here, there is a chance that while paused a file is modified and
    //     //     // then after unpausing the file is not reindexed
    //     //     return;
    //     // }
    //     const workspaceDir = await this.getWorkspaceDir(file);
    //     if (!workspaceDir) {
    //         return;
    //     }

    //     const stats = await FileUtils.getLastModifiedMap([file]);
    //     const indexesToBuild = [new EmbeddingCodebaseIndex()];
    //     for (const index of indexesToBuild) {
    //         const tag = IndexUtils.getIndexTag(index);
    //         const results =
    //             await getAddRemoveForTag(
    //                 tag,
    //                 { ...stats },
    //             );
    //         // since this is only a single file update / save we do not want to actualy remove anything, we just want to recompute for our single file
    //         results.del = [];

    //         // Don't update if nothing to update. Some of the indices might do unnecessary setup work
    //         if (results.add.length + results.update.length === 0) {
    //             continue;
    //         }

    //         const fileOperationResults = {
    //             lastModified: results.lastModified,
    //             operationList: indexResultsToIndexOperations(results)
    //         };
    //         // 执行更新
    //         const indexResult = await index.update(fileOperationResults);
    //         if (indexResult.status === 'done') {
    //             await saveAddRemoveForTag(tag, fileOperationResults);
    //         }
    //     }
    // }

    // public async deleteFile(files: string[]): Promise<void> {
    //     console.log(`[RepoIndexWatcher][deleteFile][files: ${files}]`); // Log files being deleted
    //     if (files.length === 0) {
    //         return;
    //     }
    //     const workspaceDir = await this.getWorkspaceDir(files[0]);
    //     if (!workspaceDir) {
    //         return;
    //     }
    //     const indexesToBuild = this.getIndexesToBuild();
    //     for (const index of indexesToBuild) {
    //         const tag = IndexUtils.getIndexTag(index);
    //         const results = await getRemoveForTag(tag, files);
    //         if (results.del.length === 0) {
    //             continue;
    //         }

    //         const fileOperationResults = {
    //             lastModified: results.lastModified,
    //             operationList: indexResultsToIndexOperations(results)
    //         };
    //         // 执行更新
    //         const indexResult = await index.update(fileOperationResults);
    //         if (indexResult.status === 'done') {
    //             await saveAddRemoveForTag(tag, fileOperationResults);
    //         }
    //     }
    // }

    /*
     * TODO 王文斌【高优先级】：可优化，需要根据内容长度切片
     * TODO 这里最大可能会发送200个文件，可以给予内容长度 + 文件个数综合处理
     */
    private *batchRefreshIndexResults(
        operationResults: FileOperationResults,
    ): Generator<FileOperationResults> {
        let curPos = 0;
        while (curPos < operationResults.operationList.length) {
            yield {
                operationList: operationResults.operationList.slice(curPos, curPos + this.filesPerBatch),
                lastModified: operationResults.lastModified
            };
            curPos += this.filesPerBatch;
        }
    }

    getDelayByMode() {
        console.log(`[getDelayByMode][isSlowMode: ${this.isSlowMode}]`);
        return this.isSlowMode ? 1000 : 10;
    }

    /**
     * 缓慢的获取 stat 保证性能
     * 并且对文件进行过滤操作
     */
    getMtimeMsAndFilterUnSupportFileBySlowMode(paths: string[]): Promise<AllIndexFiles> {
        return new Promise(resolve => {
            const result: { [key: string]: number } = {};
            let enableIndexFileCount = 0;
            const unSupportFilePath: any = {};
            const addUnSupportFilePath = (supportType: string, path: string) => {
                unSupportFilePath[supportType] = unSupportFilePath[supportType] || [];
                unSupportFilePath[supportType].push(path);
            };

            const onResolve = () => {
                console.log('[RepoIndexWatcher][filter][unsupport]', JSON.stringify(unSupportFilePath));
                resolve(result);
            };
            // 批处理个数
            const batchSize = 100;
            const processBatch = async (startIndex: number) => {
                const delay = this.getDelayByMode();
                const endIndex = Math.min(startIndex + batchSize, paths.length);
                const batch = paths.slice(startIndex, endIndex);
                for (const path of batch) {
                    const fileStat = FileUtils.getFileStat(path);
                    // 过滤异常文件
                    if (!fileStat) {
                        addUnSupportFilePath("error", path);
                        continue;
                    }
                    // 过滤非文件
                    if (!fileStat.isFile()) {
                        addUnSupportFilePath("unFile", path);
                        continue;
                    }
                    if (fileStat.size === 0) {
                        addUnSupportFilePath("empty", path);
                        continue;
                    }
                    // 过滤大文件
                    if (fileStat.size > LIMIT_FILE_CONTENT_LENGTH) {
                        addUnSupportFilePath("overLimit", path);
                        continue;
                    }
                    // 过滤二进制文件
                    const isBinary = await isBinaryFile(path).catch(() => false);
                    if (isBinary) {
                        addUnSupportFilePath("isBinary", path);
                        continue;
                    }

                    const mtimeMs = fileStat?.mtimeMs;
                    if (mtimeMs !== undefined) {
                        result[path] = mtimeMs;
                        enableIndexFileCount++;
                    }
                }
                // 如果有效文件数量已经大于最大值，后面就不要再处理了
                if (enableIndexFileCount > LIMIT_INDEX_WORKSPACE_FILE_COUNT + batchSize) {
                    onResolve();
                    return;
                }
                console.log(`[processBatch][startIndex: ${startIndex}]`, result);
                if (endIndex < paths.length) {
                    setTimeout(() => processBatch(endIndex), delay);
                } else {
                    onResolve();
                }
            };
            processBatch(0);
        });
    }

    private async *indexFiles(
        allIndexFiles: AllIndexFiles
    ): AsyncGenerator<IndexingProgressUpdate> {
        const indexFilesLength = Object.keys(allIndexFiles).length;
        // 如果没有需要索引的文件，不进行后面的动作
        if (indexFilesLength === 0) {
            return;
        }

        console.log(`[indexFiles][workspaceDirs: ${IndexUtils.getIndexWorkspacePaths()}, indexFilesLength: ${indexFilesLength}]`); // Log essential parameters
        IndexLog.indexFileCount(indexFilesLength);

        // 计算新增、修改、删除结果
        const indexesToBuild = this.getIndexesToBuild();
        let completedIndexCount = 0;
        let progress = 0;
        let indexError = false;
        let indexErrorMessage = [];
        
        for (const codebaseIndex of indexesToBuild) {
            // 获取该索引的所有标签
            const tags: IndexTag[] = IndexUtils.getIndexTags(codebaseIndex);
            
            // 预先为每个标签准备好过滤后的文件和操作结果
            const tagOperations: { 
                tag: IndexTag, 
                operationsResults: FileOperationResults, 
                totalOps: number,
                completedOps: number,
                errorOps: number,
                batches: FileOperationResults[]
            }[] = [];

            // 先为所有标签准备好数据
            for (const tag of tags) {
                // 1. 标记 indexing 开始索引
                yield {
                    progress: progress,
                    desc: `Planning changes for ${codebaseIndex.artifactId} index with tag ${tag.artifactId}...`,
                    status: "indexing",
                };

                // 根据标签的工作区路径过滤文件
                const tagWorkspacePath = tag.directory;
                const tagFiles: AllIndexFiles = {};

                // 只保留以工作区路径为前缀的文件
                for (const [filePath, lastModified] of Object.entries(allIndexFiles)) {
                    if (filePath.startsWith(tagWorkspacePath)) {
                        tagFiles[filePath] = lastModified;
                    }
                }

                console.log(`[indexFiles][tag: ${tag.artifactId}] 过滤后的文件数量: ${Object.keys(tagFiles).length}`);

                // 2. 计算文件的变化情况，只传入当前标签对应工作区的文件
                const results = await getAddRemoveForTag(tag, tagFiles);

                const operationsResults: FileOperationResults = {
                    operationList: indexResultsToIndexOperations(results),
                    lastModified: results.lastModified
                };

                const totalOps = operationsResults.operationList.length;

                console.log(`[indexFiles][tag: ${tag.artifactId}][operationCounts Add: ${results.add.length}, Update: ${results.update.length}, Modify: ${results.modify.length}, Delete: ${results.del.length}]`); // Log counts of operations

                // 如果有操作需要执行，将其添加到队列中
                if (totalOps > 0) {
                    // 预先将操作分批
                    const batches = Array.from(this.batchRefreshIndexResults(operationsResults));

                    tagOperations.push({
                        tag,
                        operationsResults,
                        totalOps,
                        completedOps: 0,
                        errorOps: 0,
                        batches
                    });
                }
            }

            // 现在对所有标签的所有批次进行处理
            const totalTagOps = tagOperations.reduce((sum, op) => sum + op.totalOps, 0);
            let processedOps = 0;

            // 处理所有标签的所有批次
            for (const tagOp of tagOperations) {
                for (const subResult of tagOp.batches) {
                    // 使用tag参数调用update方法
                    const indexResult = await codebaseIndex.update(tagOp.tag, subResult);
                    console.log(`[indexFiles][tag: ${tagOp.tag.artifactId}][updateResult: ${JSON.stringify(indexResult)}]`);

                    if (indexResult.status === 'done') {
                        await saveAddRemoveForTag(tagOp.tag, subResult);
                        const failedFilePaths = indexResult.failedFilePaths || [];
                        const successfulOps = subResult.operationList.length - failedFilePaths.length;

                        tagOp.completedOps += successfulOps;
                        tagOp.errorOps += failedFilePaths.length;
                        processedOps += successfulOps;

                        // 计算总体进度
                        progress = totalTagOps > 0 ? processedOps / totalTagOps : 0;
                        progress = parseFloat(Number(progress).toFixed(2));

                        console.log(`[indexFiles][tag: ${tagOp.tag.artifactId}][progress: ${progress}]`);
                        yield {
                            progress: progress,
                            desc: indexResult.desc,
                            status: "indexing",
                        };
                    } else {
                        // 标记索引过程中，存在失败 某一批次的失败不定义为失败，最终以数据库中成功文件的占比作为失败的定义
                        indexErrorMessage.push(`索引失败(不暂停索引): tag: ${tagOp.tag.artifactId}, indexResult:${JSON.stringify(indexResult)}; subResult: ${JSON.stringify(subResult)} `);
                    }

                    // 为了保证不卡顿，在慢速模式下，每次处理完睡眠一段时间
                    await sleep(this.getDelayByMode());
                }

            }
            // 后期重构成流式使用，本次不用 2024-11-18 @yueyin
            // await this.indexFilesByMode({
            //     tag,
            //     workspaceFiles,
            //     codebaseIndex,
            //     completedIndexCount,
            //     progress,
            //     indexErrorMessage,
            //     indexesToBuildLength: indexesToBuild.length
            // });

            completedIndexCount += 1;
            const workspaceProgress = await this.getWorkspaceIndexProgress(allIndexFiles);
            console.log(`[indexFiles][WorkspaceProgress: ${workspaceProgress}]`);
            this.clearLock();

            if (workspaceProgress < this.successPercent) {
                indexError = true;
                indexErrorMessage.push(`索引失败: 最终索引进度为:${workspaceProgress}`);
            }

            console.log('indexErrorMessage', indexError, indexErrorMessage.join("\n\n"));
            if (indexError) {
                yield {
                    progress: workspaceProgress,
                    desc: "索引失败",
                    status: "failed",
                };
                return;
            }

            yield {
                progress: workspaceProgress,
                desc: '索引成功',
                status: "done",
            };
        }
    }

    /**
     * 根据不同的状态走不通的模式进行 indexFiles 对性能进行优化
     * // 后期重构成流式使用，本次不用 2024-11-18 @yueyin
     */
    // async *indexFilesByMode(
    //     params:
    //         {
    //             tag: IndexTag,
    //             workspaceFiles: Uri[],
    //             codebaseIndex: CodebaseIndex,
    //             completedIndexCount: number,
    //             progress: number,
    //             indexesToBuildLength: number
    //             indexErrorMessage: string[],
    //         }
    // ) {
    //     let { tag, workspaceFiles, codebaseIndex, progress, completedIndexCount, indexesToBuildLength, indexErrorMessage } = params;
    //     // 2. 计算文件的变化情况
    //     const results = await getRemoveForTagByFiles(tag, workspaceFiles);

    //     const operationsResults: FileOperationResults = {
    //         operationList: indexResultsToIndexOperations(results),
    //         lastModified: results.lastModified
    //     };

    //     const totalOps = operationsResults.operationList.length;

    //     let completedOps = 0;
    //     let errorOps = 0;
    //     console.log(`[indexFiles][operationCounts Add: ${results.add.length}, Update: ${results.update.length}, Modify: ${results.modify.length}, Delete: ${results.del.length}]`); // Log counts of operations

    //     // 3. 更新进展
    //     // Don't update if nothing to update. Some of the indices might do unnecessary setup work
    //     if (totalOps > 0) {
    //         for (const subResult of this.batchRefreshIndexResults(operationsResults)) {
    //             const indexResult = await codebaseIndex.update(tag, subResult, repoName);
    //             console.log(`[indexFiles][updateResult: ${JSON.stringify(indexResult)}]`);
    //             if (indexResult.status === 'done') {
    //                 await saveAddRemoveForTag(tag, subResult);
    //                 const failedFilePaths = indexResult.failedFilePaths || [];
    //                 completedOps += subResult.operationList.length - failedFilePaths.length;
    //                 errorOps += failedFilePaths.length;
    //                 progress =
    //                     (completedIndexCount + completedOps / totalOps) *
    //                     (1 / indexesToBuildLength);
    //                 console.log(`[indexFiles][progress: ${progress}]`);
    //                 yield {
    //                     progress: progress,
    //                     desc: indexResult.desc,
    //                     status: "indexing",
    //                 };
    //             } else {
    //                 // 标记索引过程中，存在失败 某一批次的失败不定义为失败，最终以数据库中成功文件的占比作为失败的定义
    //                 // TODO: 某一批次失败可以添加一下retry操作
    //                 // indexError = true;
    //                 indexErrorMessage.push(`索引失败(不暂停索引): indexResult:${JSON.stringify(indexResult)}; subResult: ${JSON.stringify(subResult)} `);
    //             }
    //         }
    //     }
    // }

    /**
     * 
     * 这个方法用于正常流程没办法走下去，但是又需要判断索引是否成功的情况
     * 
     */
    // async checkPreviousProgress() {
    //     const workspaceProgress = await this.getWorkspaceIndexProgress();
    //     let indexError = false;
    //     console.log(`[checkPreviousProgress][WorkspaceProgress: ${workspaceProgress}]`);
    //     if (workspaceProgress < this.successPercent) {
    //         indexError = true;
    //     }
    //     if (indexError) {
    //         this.indexingState = {
    //             progress: workspaceProgress,
    //             desc: "索引失败",
    //             status: "failed",
    //         };
    //         return;
    //     }
    //     this.indexingState = {
    //         progress: workspaceProgress,
    //         desc: '索引成功',
    //         status: "done",
    //     };
    // }
    public async clearIndex(index: CodebaseIndex) {
        // 获取所有标签并清除
        const tags: IndexTag[] = IndexUtils.getIndexTags(index);
        for (const tag of tags) {
            try {
                // 清空索引
                const success = await index.clear(tag);
                if (!success) {
                    console.error(`[RepoIndexWatcher][clearIndexIfNeeded][Failed to clear index for tag: ${JSON.stringify(tag)}]`); // Log failed to clear index with tag
                    // throw new Error('Failed to clear index');
                    return {
                        type: ClearResultType.CLEAR_ERROR,
                        message: '清理远程索引异常'
                    };
                }
            } catch (error) {
                console.error(`[RepoIndexWatcher][clearIndexIfNeeded][Failed to clear index for tag: ${JSON.stringify(tag)}]`, error);
                return {
                    type: ClearResultType.CLEAR_ERROR,
                    message: '清理远程索引异常'
                };
            }
            try {
                // 清空进度
                await clearForTag(tag);
            } catch (error) {
                console.error(`[RepoIndexWatcher][clearIndexIfNeeded][Failed to clear progress for tag: ${JSON.stringify(tag)}]`, error);
                return {
                    type: ClearResultType.CLEAR_ERROR,
                    message: '清理本地索引异常' + (error instanceof Error ? error.message : '')
                };
            }
            console.log(`[RepoIndexWatcher][clearIndexIfNeeded][clearForTag for tag: ${JSON.stringify(tag)}]`); // Log clearForTag with tag
        }
        return {
            type: ClearResultType.CLEARED,
        };
    }

    public async clearIndexIfNeeded(): Promise<ClearIndexResult> {
        console.log(`[RepoIndexWatcher][clearIndexIfNeeded]`); // Log start of clearIndexIfNeeded
        const workspaceDirs = this.getIndexWorkspaceDirs();
        if (!workspaceDirs || workspaceDirs.length === 0) {
            return {
                type: ClearResultType.CLEAR_ERROR,
                message: '没有打开的项目'
            };
        }
        try {
            const indexesToBuild = this.getIndexesToBuild();
            for (const index of indexesToBuild) {
                // 获取所有标签
                const tags: IndexTag[] = IndexUtils.getIndexTags(index);

                // 收集需要清除和不需要清除的标签
                let clearedTags: IndexTag[] = [];
                let notClearedTags: IndexTag[] = [];
                let allFailedFilePaths: string[] = [];
                // 检查所有tag
                for (const tag of tags) {
                    // 判断是否需要重新索引
                    const { shouldClear, failedFilePaths = [] } = await index.shouldClear(tag);
                    console.log(`[RepoIndexWatcher][clearIndexIfNeeded][shouldClearResult: ${shouldClear}, tag: ${JSON.stringify(tag)}]`); // Log shouldClearResult and tag

                    if (shouldClear) {
                        // 清除该标签的索引
                        try {
                            // 清空索引
                            const success = await index.clear(tag);
                            if (!success) {
                                console.error(`[RepoIndexWatcher][clearIndexIfNeeded][Failed to clear index for tag: ${JSON.stringify(tag)}]`);
                                return {
                                    type: ClearResultType.CLEAR_ERROR,
                                    message: '清理远程索引异常'
                                };
                            }
                        } catch (error) {
                            console.error(`[RepoIndexWatcher][clearIndexIfNeeded][Failed to clear index for tag: ${JSON.stringify(tag)}]`, error);
                            return {
                                type: ClearResultType.CLEAR_ERROR,
                                message: '清理远程索引异常'
                            };
                        }
                        try {
                            // 清空进度
                            await clearForTag(tag);
                        } catch (error) {
                            console.error(`[RepoIndexWatcher][clearIndexIfNeeded][Failed to clear progress for tag: ${JSON.stringify(tag)}]`, error);
                            return {
                                type: ClearResultType.CLEAR_ERROR,
                                message: '清理本地索引异常' + (error instanceof Error ? error.message : '')
                            };
                        }
                        console.log(`[RepoIndexWatcher][clearIndexIfNeeded][clearForTag for tag: ${JSON.stringify(tag)}]`);
                        clearedTags.push(tag);
                    } else {
                        notClearedTags.push(tag);
                        allFailedFilePaths = [...allFailedFilePaths, ...failedFilePaths];
                    }
                }

                // 如果有任何标签被清除，返回CLEARED状态
                if (clearedTags.length > 0) {
                    return {
                        type: ClearResultType.CLEARED,
                        clearedTags,
                        notClearedTags
                    };
                }

                // 所有tag都检查完毕，都不需要清除
                return {
                    type: ClearResultType.UN_NEED_CLEAR,
                    failedFilePaths: allFailedFilePaths,
                    notClearedTags
                };
            }
        } catch (e: Error | any) {
            console.error(`[RepoIndexWatcher][clearIndexIfNeeded][error: ${e.message}]`);
            return {
                type: ClearResultType.CLEAR_ERROR,
                message: e instanceof Error ? e.message : '清理索引异常'
            };
        }
        console.log(`[RepoIndexWatcher][clearIndexIfNeeded][end]`); // Log end of clearIndexIfNeeded
        return {
            type: ClearResultType.CLEAR_ERROR,
            message: '未触发清除逻辑'
        };
    }

    async reindex() {
        await this.lock(async () => {
            // 先设置为clearing状态，因为大仓库clear有一定耗时，避免用户在此期间再次点击
            this.indexingState = {
                status: "clearing",
                progress: this.indexingState.progress, // 保持原有进度
                desc: "索引清理中...",
            };

            const indexesToBuild = this.getIndexesToBuild();
            for (const index of indexesToBuild) {
                // 手动触发时，清除所有仓库的索引
                const clearStatus = await this.clearIndex(index);
                if (clearStatus.type === ClearResultType.CLEAR_ERROR) {
                    this.indexingState = IndexProgress.clearError(clearStatus.message || "清除索引异常");
                    return;
                }
            }
            // 手动触发时，不传递notClearedTags，表示全部重新索引
            await this.refresh(true);
        });
    }
    // 这方法调用处注释掉了（实时监听文件变更做增量index），暂时不用做多仓改造
    // private async getWorkspaceDir(filepath: string): Promise<WorkspaceFolder | undefined> {
    //     const workspaceDirs = vscode.workspace.workspaceFolders || [];
    //     if (workspaceDirs.length === 0) {
    //         return undefined;
    //     }
    //     const workspaceDir = workspaceDirs[0];
    //     if (filepath.startsWith(IndexUtils.getIndexWorkspacePath())) {
    //         return workspaceDir;
    //     }
    //     return undefined;
    // }

    private getIndexesToBuild(): CodebaseIndex[] {
        return [
            new EmbeddingCodebaseIndex()
        ];
    }

    private logProgress(
        beginTime: number,
        completedFileCount: number,
        progress: number,
    ) {
        const timeTaken = Date.now() - beginTime;
        const seconds = Math.round(timeTaken / 1000);
        const progressPercentage = (progress * 100).toFixed(1);
        const filesPerSec = (completedFileCount / seconds).toFixed(2);
        // console.debug(
        //   `Indexing: ${progressPercentage}% complete, elapsed time: ${seconds}s, ${filesPerSec} file/sec`,
        // );
    }

    getIndexingState(): IndexingProgressUpdate {
        return this.indexingState;
    }

    dispose() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = undefined;
        }
    }

}
