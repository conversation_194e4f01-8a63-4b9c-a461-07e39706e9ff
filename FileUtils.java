package com.sankuai.mcopilot.dataset.codebase.eval.utils;

import com.amazonaws.util.IOUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class FileUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> List<T> processJsonFile(String jsonFilePath, Class<T> clazz) throws IOException {
        List<T> instanceList = new ArrayList<>();
        JsonNode jsonArray = objectMapper.readTree(new File(jsonFilePath));

        for (JsonNode jsonNode : jsonArray) {
            T instance = objectMapper.treeToValue(jsonNode, clazz);
            instanceList.add(instance);
        }
        return instanceList;
    }

    public static void extractFromZip(String projectName, String zipFilePath, String extractPath) throws IOException {
        // Extract the zip file using Apache Commons Compress
        try (ZipFile zipFile = new ZipFile(zipFilePath)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                String entryName = entry.getName();
                // Remove the top-level directory if it exists
                if (entryName.startsWith(projectName + "/")) {
                    entryName = entryName.substring(projectName.length() + 1);
                }
                if (entryName.isEmpty()) {
                    continue;
                }
                File entryDestination = new File(extractPath, entryName);
                if (entry.isDirectory()) {
                    entryDestination.mkdirs();
                } else {
                    entryDestination.getParentFile().mkdirs();
                    try (InputStream in = zipFile.getInputStream(entry);
                         OutputStream out = Files.newOutputStream(entryDestination.toPath())) {
                        IOUtils.copy(in, out);
                    }
                }
            }
        }
    }

    public static List<String> traverseDirectory(String directory) {
        List<String> filePaths = new ArrayList<>();
        File dir = new File(directory);
        traverseDirsRecursive(dir, filePaths);
        return filePaths;
    }

    private static void traverseDirsRecursive(File dir, List<String> filePaths) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    traverseDirsRecursive(file, filePaths);
                } else {
                    filePaths.add(file.getAbsolutePath());
                }
            }
        }
    }

    public static String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filePath.substring(lastDotIndex);
        }
        return "";
    }

    public static void delete(String filePath) {
        new File(filePath).delete();
    }

    public static String readFile(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)));
    }
}
