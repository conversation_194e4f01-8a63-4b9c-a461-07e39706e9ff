package main

import (
	"fmt"
)

func BubbleSort(arr []int) {
	n := len(arr)
	for i := 0; i < n-1; i++ {
		swapped := false
		for j := 0; j < n-i-1; j++ {
			if arr[j] > arr[j+1] {
				arr[j], arr[j+1] = arr[j+1], arr[j]
				swapped = true
			}
		}
		if !swapped {
			break
		}
	}
}
		}
	}
}// BubbleSortDesc 实现降序冒泡排序
func BubbleSortDesc(arr []int) {
	n := len(arr)
	for i := 0; i < n-1; i++ {
		swapped := false
		for j := 0; j < n-i-1; j++ {
			// 降序排序，比较方向相反
			if arr[j] < arr[j+1] {
				arr[j], arr[j+1] = arr[j+1], arr[j]
				swapped = true
			}
		}
		if !swapped {
			break
		}
	}
}

func main() {
	// 测试升序排序
	arr1 := []int{64, 34, 25, 12, 22, 11, 90}
	fmt.Println("原始数组:", arr1)
	BubbleSort(arr1)
	fmt.Println("升序排序后:", arr1)

	// 测试降序排序
	arr2 := []int{64, 34, 25, 12, 22, 11, 90}
	fmt.Println("原始数组:", arr2)
	BubbleSortDesc(arr2)
	fmt.Println("降序排序后:", arr2)
}
